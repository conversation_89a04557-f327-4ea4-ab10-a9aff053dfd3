import 'package:equatable/equatable.dart';

enum PostStatus { draft, scheduled, published, failed }

enum PostType { text, image, video, carousel }

class Post extends Equatable {
  final String id;
  final String userId;
  final String content;
  final PostType type;
  final PostStatus status;
  final List<String> platforms;
  final List<PostMedia> media;
  final DateTime? scheduledAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final PostAnalytics? analytics;

  const Post({
    required this.id,
    required this.userId,
    required this.content,
    required this.type,
    required this.status,
    required this.platforms,
    required this.media,
    this.scheduledAt,
    required this.createdAt,
    required this.updatedAt,
    this.analytics,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        content,
        type,
        status,
        platforms,
        media,
        scheduledAt,
        createdAt,
        updatedAt,
        analytics,
      ];
}

class PostMedia extends Equatable {
  final String id;
  final String url;
  final String type; // image, video
  final String? thumbnail;
  final Map<String, dynamic>? metadata;

  const PostMedia({
    required this.id,
    required this.url,
    required this.type,
    this.thumbnail,
    this.metadata,
  });

  @override
  List<Object?> get props => [id, url, type, thumbnail, metadata];
}

class PostAnalytics extends Equatable {
  final String postId;
  final int views;
  final int likes;
  final int shares;
  final int comments;
  final double engagementRate;
  final Map<String, int> platformStats;

  const PostAnalytics({
    required this.postId,
    required this.views,
    required this.likes,
    required this.shares,
    required this.comments,
    required this.engagementRate,
    required this.platformStats,
  });

  @override
  List<Object?> get props => [
        postId,
        views,
        likes,
        shares,
        comments,
        engagementRate,
        platformStats,
      ];
}
