<?php
// Simple API test script
header('Content-Type: application/json');

echo "Testing Nazmart API endpoints...\n\n";

// Test 1: Check if the API route exists
$api_url = 'http://localhost/Vendora/nazmart-bundle-v2.6.0/api/create-store';

echo "1. Testing API endpoint: $api_url\n";

// Test with cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'store_name' => 'teststore',
    'phone' => '1234567890',
    'category' => 'electronics'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

// Test 2: Check BeePost store creation page
echo "2. Testing BeePost store creation page...\n";
$beepost_url = 'http://localhost/Vendora/nazmart-bundle-v2.6.0/kode/public/admin/store/create';
echo "BeePost URL: $beepost_url\n";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $beepost_url);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_TIMEOUT, 10);
curl_setopt($ch2, CURLOPT_FOLLOWLOCATION, true);

$response2 = curl_exec($ch2);
$http_code2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
$error2 = curl_error($ch2);
curl_close($ch2);

echo "HTTP Code: $http_code2\n";
if ($error2) {
    echo "cURL Error: $error2\n";
} else {
    echo "Page accessible: " . (strpos($response2, 'Create New Store') !== false ? 'YES' : 'NO') . "\n";
}

echo "\nTest completed.\n";
?>
