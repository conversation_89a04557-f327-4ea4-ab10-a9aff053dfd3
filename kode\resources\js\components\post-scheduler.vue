<template>
  <div class="post-scheduler-wrapper">
    <!-- Platform Selection with Store Links -->
    <div class="platform-selection">
      <h4 class="card-title mb-3">{{ translate('Choose Profile') }}</h4>
      
      <div class="platform-accounts">
        <div v-for="account in accounts" :key="account.id" class="account-item">
          <div class="account-checkbox-wrapper">
            <input 
              type="checkbox" 
              :id="`account_${account.id}`"
              :value="account.id"
              v-model="selectedAccounts"
              class="account-checkbox"
            />
            <label :for="`account_${account.id}`" class="account-label">
              <img :src="account.avatar" :alt="account.name" class="account-avatar" />
              <span class="account-info">
                <span class="account-name">{{ account.name }}</span>
                <span class="platform-name">{{ account.platform_name }}</span>
              </span>
            </label>
            
            <!-- Store Link Icon -->
            <div 
              v-if="shouldShowStoreLink(account.platform_slug)" 
              class="store-link-wrapper"
              :title="translate('Store Link')"
            >
              <button 
                type="button"
                @click="openStorePreview(account.platform_slug)"
                class="store-link-btn"
                :disabled="!hasNazmartStore"
              >
                <i class="bi bi-shop" :class="{ 'text-muted': !hasNazmartStore }"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Store Preview Modal -->
    <div 
      v-if="showStoreModal" 
      class="modal fade show d-block" 
      tabindex="-1" 
      style="background-color: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ translate('Store Preview') }}</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="closeStoreModal"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="!hasNazmartStore" class="text-center py-4">
              <i class="bi bi-shop fs-1 text-muted mb-3"></i>
              <h5>{{ translate('No Store Connected') }}</h5>
              <p class="text-muted">{{ translate('Connect your Nazmart store to share products on social media.') }}</p>
              <a href="/admin/store/create" class="btn btn-primary">
                {{ translate('Create Store') }}
              </a>
            </div>
            <div v-else class="store-preview">
              <div class="store-info mb-3">
                <h6>{{ translate('Your Store') }}</h6>
                <p class="mb-1"><strong>{{ translate('Store URL') }}:</strong></p>
                <a :href="storeUrl" target="_blank" class="text-primary">
                  {{ storeUrl }}
                </a>
              </div>
              <div class="store-actions">
                <a 
                  :href="storeUrl" 
                  target="_blank" 
                  class="btn btn-outline-primary me-2"
                >
                  <i class="bi bi-box-arrow-up-right me-1"></i>
                  {{ translate('Visit Store') }}
                </a>
                <button 
                  type="button" 
                  @click="copyStoreUrl" 
                  class="btn btn-outline-secondary"
                >
                  <i class="bi bi-clipboard me-1"></i>
                  {{ translate('Copy URL') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PostScheduler',
  props: {
    accounts: {
      type: Array,
      default: () => []
    },
    userStoreData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectedAccounts: [],
      showStoreModal: false,
      currentPlatform: null
    }
  },
  computed: {
    hasNazmartStore() {
      return this.userStoreData && this.userStoreData.nazmart_store_slug;
    },
    storeUrl() {
      if (!this.hasNazmartStore) return '';
      return `https://vendora.com/stores/${this.userStoreData.nazmart_store_slug}`;
    }
  },
  methods: {
    shouldShowStoreLink(platformSlug) {
      return ['instagram', 'whatsapp', 'facebook'].includes(platformSlug.toLowerCase());
    },
    openStorePreview(platformSlug) {
      this.currentPlatform = platformSlug;
      this.showStoreModal = true;
    },
    closeStoreModal() {
      this.showStoreModal = false;
      this.currentPlatform = null;
    },
    async copyStoreUrl() {
      try {
        await navigator.clipboard.writeText(this.storeUrl);
        this.showToast('Store URL copied to clipboard!', 'success');
      } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = this.storeUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.showToast('Store URL copied to clipboard!', 'success');
      }
    },
    showToast(message, type = 'info') {
      // Simple toast notification - can be enhanced with a proper toast library
      const toast = document.createElement('div');
      toast.className = `alert alert-${type} position-fixed`;
      toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
      toast.textContent = message;
      
      document.body.appendChild(toast);
      
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 3000);
    },
    translate(key) {
      // Simple translation function - integrate with your existing translation system
      return window.translations && window.translations[key] ? window.translations[key] : key;
    }
  },
  watch: {
    selectedAccounts(newVal) {
      // Emit selected accounts to parent component
      this.$emit('accounts-selected', newVal);
    }
  }
}
</script>

<style scoped>
.post-scheduler-wrapper {
  width: 100%;
}

.platform-accounts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.account-item:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.account-checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-checkbox {
  margin: 0;
}

.account-label {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
  margin: 0;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.account-info {
  display: flex;
  flex-direction: column;
}

.account-name {
  font-weight: 500;
  color: #333;
}

.platform-name {
  font-size: 0.875rem;
  color: #666;
}

.store-link-wrapper {
  margin-left: auto;
}

.store-link-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #007bff;
}

.store-link-btn:hover:not(:disabled) {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.store-link-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.store-preview {
  padding: 20px 0;
}

.store-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.store-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .account-checkbox-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .store-link-wrapper {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .store-actions {
    flex-direction: column;
  }
  
  .store-actions .btn {
    width: 100%;
  }
}
</style>
