<?php

/**
 * Enhanced AI Workflow Testing Script
 * 
 * This script tests the complete product-first AI workflow implementation
 * including product creation, AI content generation, and platform-specific formatting.
 */

require_once __DIR__ . '/kode/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Services\ProductFirstWorkflowService;
use App\Http\Services\AiService;
use App\Services\Nazmart\ProductUploadService;

class EnhancedAiWorkflowTester
{
    private $workflowService;
    private $aiService;
    private $productService;

    public function __construct()
    {
        $this->workflowService = new ProductFirstWorkflowService();
        $this->aiService = new AiService();
        $this->productService = new ProductUploadService();
    }

    /**
     * Test 1: Product-First Workflow with Website Creation
     */
    public function testProductFirstWorkflowWithWebsite()
    {
        echo "=== Test 1: Product-First Workflow with Website Creation ===\n";

        $testData = [
            'product_name' => 'Premium Wireless Headphones',
            'product_description' => 'High-quality wireless headphones with noise cancellation and 30-hour battery life. Perfect for music lovers and professionals.',
            'product_price' => 199.99,
            'product_category' => 'Electronics',
            'product_image' => 'https://example.com/headphones.jpg',
            'target_platforms' => ['instagram', 'facebook', 'whatsapp', 'twitter'],
            'post_to_website_first' => true,
            'language' => 'English',
            'tone' => 'Professional',
            'include_hashtags' => true,
            'include_emojis' => true,
            'include_cta' => true,
        ];

        try {
            $result = $this->workflowService->executeProductFirstWorkflow($testData);

            if ($result['success']) {
                echo "✅ Product-first workflow completed successfully\n";
                echo "Product URL: " . ($result['data']['product_data']['product_url'] ?? 'N/A') . "\n";
                echo "Platforms generated: " . count($result['data']['content']) . "\n";

                // Test platform-specific content
                foreach ($result['data']['content'] as $platform => $content) {
                    echo "Platform: $platform\n";
                    echo "Caption length: " . strlen($content['caption']) . " characters\n";
                    echo "URL handling: " . $content['url_handling'] . "\n";
                    echo "Hashtags: " . count($content['hashtags'] ?? []) . "\n";
                    echo "---\n";
                }
            } else {
                echo "❌ Product-first workflow failed: " . $result['message'] . "\n";
            }
        } catch (Exception $e) {
            echo "❌ Exception in product-first workflow: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test 2: Standard Content Generation (Non-Product)
     */
    public function testStandardContentGeneration()
    {
        echo "=== Test 2: Standard Content Generation ===\n";

        $testData = [
            'post_content' => 'Share your thoughts about the latest technology trends and how they impact our daily lives.',
            'target_platforms' => ['instagram', 'facebook', 'twitter'],
            'language' => 'English',
            'tone' => 'Casual',
            'include_hashtags' => true,
            'include_emojis' => true,
        ];

        try {
            $request = new Request($testData);
            $result = $this->aiService->generateStandardContent($request);

            if ($result['status']) {
                echo "✅ Standard content generation completed successfully\n";
                echo "Generated content length: " . strlen($result['data']) . " characters\n";

                // Parse and display content
                $content = json_decode($result['data'], true);
                if ($content) {
                    echo "Parsed JSON content successfully\n";
                    if (isset($content['caption'])) {
                        echo "Caption: " . substr($content['caption'], 0, 100) . "...\n";
                    }
                } else {
                    echo "Content as text: " . substr($result['data'], 0, 100) . "...\n";
                }
            } else {
                echo "❌ Standard content generation failed: " . $result['message'] . "\n";
            }
        } catch (Exception $e) {
            echo "❌ Exception in standard content generation: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test 3: Platform-Specific Content Formatting
     */
    public function testPlatformSpecificFormatting()
    {
        echo "=== Test 3: Platform-Specific Content Formatting ===\n";

        $testContent = [
            'caption' => 'Check out our amazing new product! 🎉 Perfect for tech enthusiasts. Get yours today! #tech #innovation #newproduct',
            'hashtags' => ['#tech', '#innovation', '#newproduct'],
            'emojis' => ['🎉', '💻', '🚀'],
            'cta' => 'Get yours today!'
        ];

        $productUrl = 'https://vendora.com/products/wireless-headphones';
        $platforms = ['instagram', 'facebook', 'whatsapp', 'twitter'];

        try {
            foreach ($platforms as $platform) {
                $methodName = 'formatFor' . ucfirst($platform);

                if (method_exists($this->workflowService, $methodName)) {
                    $formatted = $this->workflowService->$methodName($testContent, $productUrl);

                    echo "Platform: $platform\n";
                    echo "Caption length: " . strlen($formatted['caption']) . " characters\n";
                    echo "URL handling: " . $formatted['url_handling'] . "\n";

                    if (isset($formatted['note'])) {
                        echo "Note: " . $formatted['note'] . "\n";
                    }
                    echo "---\n";
                } else {
                    echo "❌ Method $methodName not found\n";
                }
            }

            echo "✅ Platform-specific formatting test completed\n";
        } catch (Exception $e) {
            echo "❌ Exception in platform formatting: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test 4: AI Service Methods
     */
    public function testAiServiceMethods()
    {
        echo "=== Test 4: AI Service Methods ===\n";

        try {
            // Test helper methods
            $platforms = ['instagram', 'facebook', 'whatsapp'];
            $platformInstructions = $this->aiService->buildPlatformInstructions($platforms);
            echo "✅ buildPlatformInstructions method works\n";
            echo "Instructions length: " . strlen($platformInstructions) . " characters\n";

            $formattedPlatforms = $this->aiService->formatPlatforms($platforms);
            echo "✅ formatPlatforms method works: " . $formattedPlatforms . "\n";

            $formattedPrice = $this->aiService->formatPrice(199.99);
            echo "✅ formatPrice method works: " . $formattedPrice . "\n";

            $boolText = $this->aiService->boolToText(true);
            echo "✅ boolToText method works: " . $boolText . "\n";

        } catch (Exception $e) {
            echo "❌ Exception in AI service methods: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test 5: Error Handling and Edge Cases
     */
    public function testErrorHandling()
    {
        echo "=== Test 5: Error Handling and Edge Cases ===\n";

        // Test with missing required fields
        try {
            $invalidData = [
                'product_name' => '', // Empty name
                'target_platforms' => [],
                'post_to_website_first' => true,
            ];

            $result = $this->workflowService->executeProductFirstWorkflow($invalidData);

            if (!$result['success']) {
                echo "✅ Error handling works for invalid data\n";
                echo "Error message: " . $result['message'] . "\n";
            } else {
                echo "❌ Should have failed with invalid data\n";
            }
        } catch (Exception $e) {
            echo "✅ Exception handling works: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "Starting Enhanced AI Workflow Tests...\n\n";

        $this->testProductFirstWorkflowWithWebsite();
        $this->testStandardContentGeneration();
        $this->testPlatformSpecificFormatting();
        $this->testAiServiceMethods();
        $this->testErrorHandling();

        echo "All tests completed!\n";
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $tester = new EnhancedAiWorkflowTester();
    $tester->runAllTests();
}
