import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String name;
  final String? avatar;
  final String? phone;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final UserSubscription? subscription;

  const User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.subscription,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatar,
        phone,
        createdAt,
        updatedAt,
        isVerified,
        subscription,
      ];
}

class UserSubscription extends Equatable {
  final String id;
  final String planName;
  final DateTime expiresAt;
  final bool isActive;
  final List<String> features;

  const UserSubscription({
    required this.id,
    required this.planName,
    required this.expiresAt,
    required this.isActive,
    required this.features,
  });

  @override
  List<Object?> get props => [id, planName, expiresAt, isActive, features];
}
