import 'package:flutter/foundation.dart';
import '../../domain/entities/post.dart';

enum DashboardStatus { initial, loading, loaded, error }

class DashboardProvider extends ChangeNotifier {
  DashboardStatus _status = DashboardStatus.initial;
  List<Post> _recentPosts = [];
  Map<String, int> _analytics = {};
  String? _errorMessage;

  DashboardStatus get status => _status;
  List<Post> get recentPosts => _recentPosts;
  Map<String, int> get analytics => _analytics;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == DashboardStatus.loading;

  Future<void> loadDashboardData() async {
    _setStatus(DashboardStatus.loading);

    try {
      // TODO: Implement actual API calls
      await Future.delayed(const Duration(seconds: 2));

      // Mock data
      _recentPosts = [
        Post(
          id: '1',
          userId: '1',
          content: 'Check out our new product launch! 🚀',
          type: PostType.text,
          status: PostStatus.published,
          platforms: ['facebook', 'instagram'],
          media: [],
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        Post(
          id: '2',
          userId: '1',
          content: 'Behind the scenes of our latest photoshoot 📸',
          type: PostType.image,
          status: PostStatus.scheduled,
          platforms: ['instagram', 'twitter'],
          media: [],
          scheduledAt: DateTime.now().add(const Duration(hours: 4)),
          createdAt: DateTime.now().subtract(const Duration(hours: 5)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
        ),
      ];

      _analytics = {
        'totalPosts': 25,
        'scheduledPosts': 8,
        'totalViews': 1250,
        'totalEngagement': 340,
      };

      _setStatus(DashboardStatus.loaded);
    } catch (e) {
      _setError('Failed to load dashboard data');
    }
  }

  Future<void> refreshData() async {
    await loadDashboardData();
  }

  void _setStatus(DashboardStatus status) {
    _status = status;
    if (status != DashboardStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(DashboardStatus.error);
  }
}
