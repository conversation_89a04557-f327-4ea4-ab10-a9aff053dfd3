@charset "UTF-8";
/* cyrillic */
@font-face {
  font-family: 'Jost';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zUtBhPNqw73oHt5D4hTxM.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Jost';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zUtBhPNqw73oHt7j4hTxM.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Jost';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zUtBhPNqw73oHt4D4h.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic */
@font-face {
  font-family: 'Jost';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zatBhPNqw73oDd4iYl.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Jost';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zatBhPNqw73ord4iYl.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Jost';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/jost/v18/92zatBhPNqw73oTd4g.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}



:root {
  --font-primary: "Jost", sans-serif;
  --color-primary: #7f56d9;
  --color-primary-light: rgba(175, 0, 255, 0.08);
  --color-primary-text: #fff;

  --color-secondary: rgba(255, 192, 71, 1);
  --color-secondary-light: rgba(195, 250, 107, 0.1);
  --color-secondary-soft: #f1faec;
  --color-secondary-text: #24282c;

  --text-primary: rgba(27, 28, 30, 1)
  --text-secondary: rgba(76, 75, 75, 1);
  --text-light: rgb(133, 142, 164);
  --border-one: rgba(205, 205, 205, 0.65);
  --border-two: rgba(233, 237, 247, 1);
  --color-white: #fff;
  --site-bg: rgba(250, 251, 252, 1);
  --bg-light-one: rgba(239, 240, 244, 1);
  --bg-light-two: rgba(245, 245, 245, 1);
  --card-bg-light: #EAECF0;
  --color-dark: rgba(27, 28, 30, 1);


  --color-success: rgba(0, 194, 147, 1);
  --color-success-light: rgba(0, 194, 147, 0.1);

  --color-danger: rgba(255, 26, 26, 1);
  --color-danger-light: rgba(255, 26, 26, 0.1);
  --color-danger-light-2: rgba(255, 26, 26, 0.05);

  --color-warning: #f6c000;
  --color-warning-light: rgba(245, 192, 0, 0.1);
  --color-warning-soft: #fff8dd;

  --color-info: rgba(35, 133, 255, 1);
  --color-info-light: rgba(38, 135, 255, 0.1);

  --color-purple: #AA75F5;
  --color-purple-light: #f1e7ff;
  --color-blue: #70ccff;
  --color-blue-light: #e5f5ff;
  --color-orange: #ff9e6b;
  --color-orange-light: #ffefe7;
  --color-green: #63d27c;
  --color-green-light: #ebfeef;
}

.g-5, .gy-5 {
  --bs-gutter-y: 2.5rem;
}

* {
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
  margin-bottom: 0;
  font-weight: 500;
  line-height: 1.3;
}


p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

a,
a:active,
a:visited,
a:link {
  text-decoration: none;
}

ul,
ol,
li {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

body {
  height: 100%;
  line-height: 1.75;
  color: var(--text-secondary);
  font-family: var(--font-primary) !important;
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: antialiased;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  outline: none;
  border: none;
}

figure {
  margin-bottom: 0 !important;
}

.text--primary {
  color: var(--color-primary) !important;
}
.text--secondary {
  color: var(--color-secondary) !important;
}
.text--light {
  color: var(--text-light) !important;
}

.text--danger {
  color: var(--color-danger) !important;
}

.text--info {
  color: var(--color-info) !important;
}

.text--warning {
  color: var(--color-warning) !important;
}

.text--success {
  color: var(--color-success) !important;
}

.text--dark {
  color: var(--color-dark) !important;
}

.fw-bold{
  font-weight: 500;
}

.bg-primary-solid {
  background-color: var(--color-primary);
}
.bg-primary-soft {
  background-color: var(--color-primary-soft);
}

.bg-secondary-solid {
  background-color: var(--color-secondary);
}
.bg-secondary-soft {
  background-color: var(--color-secondary-soft);
}

.bg-warning-solid {
  background-color: var(--color-warning);
}
.bg-warning-soft {
  background-color: var(--color-warning-soft);
}

.bg-info-solid {
  background-color: var(--color-info);
}
.bg-info-soft {
  background-color: var(--color-info-soft);
}

.bg-danger-solid {
  background-color: var(--color-danger);
}
.bg-danger-soft {
  background-color: var(--color-danger-light) !important;
}

.bg-success-solid {
  background-color: var(--color-success);
}
.bg-success-soft {
  background-color: var(--color-success-soft);
}

.linear-bg {
  background: linear-gradient(90deg,rgba(201, 240, 255, 0.6),rgba(233, 200, 249, 0.6));
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pt-110 {
  padding-top: 110px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .pt-110 {
    padding-top: 75px;
  }
}
@media (max-width: 767px) {
  .pt-110 {
    padding-top: 70px;
  }
}

.pb-110 {
  padding-bottom: 110px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .pb-110 {
    padding-bottom: 75px;
  }
}
@media (max-width: 767px) {
  .pb-110 {
    padding-bottom: 70px;
  }
}

.fs-12{
  font-size: 12px;
}
.fs-13{
  font-size: 13px;
}
.fs-14{
  font-size: 14px;
}
.fs-15{
  font-size: 15px;
}
.fs-16{
  font-size: 16px;
}
.fs-17{
  font-size: 17px;
}
.fs-18{
  font-size: 18px;
}
.fs-19{
  font-size: 19px;
}
.fs-20{
  font-size: 20px;
}
.fs-22{
  font-size: 22px;
}
.fs-24{
  font-size: 24px;
}
.fs-28{
  font-size: 28px;
}
.fs-30{
  font-size: 30px;
}

.mb-30{
  margin-bottom: 30px !important;
}
.mb-25{
  margin-bottom: 25px;
}
.mb-20{
  margin-bottom: 20px;
}

.mb-60{
  margin-bottom: 60px;
}

@media (max-width: 991px) {
  .mb-60{
    margin-bottom: 40px;
  }
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mt-110 {
  margin-top: 110px;
}

@media (min-width: 768px) and (max-width: 991px) {
  .mt-110 {
    margin-top: 75px;
  }
}
@media (max-width: 767px) {
  .mt-110 {
    margin-top: 70px;
  }
  .fs-18{
    font-size: 16px;
  }
}

.mb-110 {
  margin-bottom: 110px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .mb-110 {
    margin-bottom: 75px;
  }
}
@media (max-width: 767px) {
  .mb-110 {
    margin-bottom: 70px;
  }
}

.p-20{
  padding: 20px;
}

@media (max-width: 1399px) {
  .p-20{
    padding: 15px;
  }
}

.p-15{
  padding: 15px;
}

.radius-8{
  border-radius: 8px;
}
.radius-12{
  border-radius: 12px;
}
.radius-16{
  border-radius: 16px;
}
.radius-20{
  border-radius: 20px;
}
.radius-30{
  border-radius: 30px;
}

input,select{
  border-radius: 30px;
  border: 1px solid var(--border-two);
}

textarea{
  border-radius: 16px !important;
  border: 1px solid var(--border-two);
}


.border{
  border: 2px solid var(--border-two) !important;
}
.border-top{
  border-top: 1px solid var(--border-two) !important;
}
.rounded-50{
  border-radius: 50%;
}
.i-card{
  padding: 20px;
  background-color: var(--color-white);
  border-radius: 20px;
}

@media (max-width: 576px) {
  .i-card{
    padding: 15px;
  }
}
.card--title{
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
}

@media (max-width: 768px){
  .card--title{
    font-size: 18px;
  }
}

.card--title-sm{
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
}
@media (min-width: 991px) and (max-width: 1399px){
  .card--title-sm{
    font-size: 16px;
  }
}
@media (max-width: 768px){
  .card--title-sm{
    font-size: 16px;
  }
}

.shadow-one{
  box-shadow: 0px 3px 15px rgba(0,0,0,0.07);
}

.img-100{
    
  max-width: 200px;
  width: 100%;
}
