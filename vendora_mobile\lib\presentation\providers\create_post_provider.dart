import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../../core/config/app_config.dart';

enum CreatePostStatus { initial, posting, success, error }

class MediaFile {
  final String path;
  final String type; // 'image' or 'video'
  final String? name;

  MediaFile({
    required this.path,
    required this.type,
    this.name,
  });
}

class CreatePostProvider extends ChangeNotifier {
  final Dio _dio = Dio();

  CreatePostStatus _status = CreatePostStatus.initial;
  String _caption = '';
  List<MediaFile> _mediaFiles = [];
  Set<String> _selectedPlatforms = {};
  String? _errorMessage;

  CreatePostStatus get status => _status;
  String get caption => _caption;
  List<MediaFile> get mediaFiles => List.unmodifiable(_mediaFiles);
  Set<String> get selectedPlatforms => Set.unmodifiable(_selectedPlatforms);
  String? get errorMessage => _errorMessage;
  
  bool get isPosting => _status == CreatePostStatus.posting;
  bool get hasContent => _caption.isNotEmpty || _mediaFiles.isNotEmpty;
  bool get canPost => hasContent && _selectedPlatforms.isNotEmpty;

  void updateCaption(String caption) {
    _caption = caption;
    notifyListeners();
  }

  void addMedia(String path, String type) {
    final mediaFile = MediaFile(
      path: path,
      type: type,
      name: path.split('/').last,
    );
    _mediaFiles.add(mediaFile);
    notifyListeners();
  }

  void removeMedia(int index) {
    if (index >= 0 && index < _mediaFiles.length) {
      _mediaFiles.removeAt(index);
      notifyListeners();
    }
  }

  void togglePlatform(String platform) {
    if (_selectedPlatforms.contains(platform)) {
      _selectedPlatforms.remove(platform);
    } else {
      _selectedPlatforms.add(platform);
    }
    notifyListeners();
  }

  void resetForm() {
    _caption = '';
    _mediaFiles.clear();
    _selectedPlatforms.clear();
    _errorMessage = null;
    _status = CreatePostStatus.initial;
    notifyListeners();
  }

  Future<bool> publishPost() async {
    if (!canPost) {
      _setError('Please add content and select at least one platform');
      return false;
    }

    _setStatus(CreatePostStatus.posting);

    try {
      // Prepare form data
      final formData = FormData();
      
      // Add caption
      formData.fields.add(MapEntry('caption', _caption));
      
      // Add selected platforms
      for (final platform in _selectedPlatforms) {
        formData.fields.add(MapEntry('platforms[]', platform));
      }
      
      // Add media files
      for (int i = 0; i < _mediaFiles.length; i++) {
        final mediaFile = _mediaFiles[i];
        final file = await MultipartFile.fromFile(
          mediaFile.path,
          filename: mediaFile.name ?? 'media_$i.${_getFileExtension(mediaFile.path)}',
        );
        formData.files.add(MapEntry('media[]', file));
      }
      
      // Add media types
      for (final mediaFile in _mediaFiles) {
        formData.fields.add(MapEntry('media_types[]', mediaFile.type));
      }

      // Special handling for WhatsApp Status
      if (_selectedPlatforms.contains('whatsapp')) {
        formData.fields.add(MapEntry('whatsapp_type', 'status'));
        formData.fields.add(MapEntry('whatsapp_render_format', 'text_image'));
      }

      // Send request to BeePost backend
      final response = await _dio.post(
        '${AppConfig.baseUrl}/api/posts',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': 'Bearer ${await _getAuthToken()}',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        _setStatus(CreatePostStatus.success);
        
        // Handle WhatsApp status specific response
        if (_selectedPlatforms.contains('whatsapp') && response.data['whatsapp_status'] != null) {
          debugPrint('WhatsApp Status created: ${response.data['whatsapp_status']}');
        }
        
        return true;
      } else {
        _setError('Failed to publish post. Please try again.');
        return false;
      }
    } on DioException catch (e) {
      _handleDioError(e);
      return false;
    } catch (e) {
      _setError('An unexpected error occurred: ${e.toString()}');
      return false;
    }
  }

  void _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        _setError('Connection timeout. Please check your internet connection.');
        break;
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        if (statusCode == 401) {
          _setError('Authentication failed. Please login again.');
        } else if (statusCode == 422) {
          final errors = e.response?.data['errors'];
          if (errors != null && errors is Map) {
            final errorMessages = <String>[];
            errors.forEach((key, value) {
              if (value is List) {
                errorMessages.addAll(value.cast<String>());
              }
            });
            _setError(errorMessages.join('\n'));
          } else {
            _setError('Validation failed. Please check your input.');
          }
        } else if (statusCode == 413) {
          _setError('File size too large. Please choose smaller files.');
        } else {
          _setError('Server error (${statusCode}). Please try again later.');
        }
        break;
      case DioExceptionType.cancel:
        _setError('Request was cancelled.');
        break;
      case DioExceptionType.connectionError:
        _setError('No internet connection. Please check your network.');
        break;
      default:
        _setError('Network error. Please try again.');
    }
  }

  String _getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }

  Future<String> _getAuthToken() async {
    // TODO: Get actual auth token from secure storage
    // For now, return a placeholder
    return 'your_auth_token_here';
  }

  void _setStatus(CreatePostStatus status) {
    _status = status;
    if (status != CreatePostStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(CreatePostStatus.error);
  }
}
