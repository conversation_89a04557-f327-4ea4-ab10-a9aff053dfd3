<?php

namespace App\Http\Services;

use App\Http\Services\AiService;
use App\Services\Nazmart\ProductUploadService;
use App\Services\Nazmart\ProductURLService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProductFirstWorkflowService
{
    protected $aiService;
    protected $productUploadService;
    protected $productURLService;

    public function __construct(AiService $aiService, ProductUploadService $productUploadService, ProductURLService $productURLService)
    {
        $this->aiService = $aiService;
        $this->productUploadService = $productUploadService;
        $this->productURLService = $productURLService;
    }

    /**
     * Execute the complete product-first workflow
     * 1. Upload product to Nazmart (if requested)
     * 2. Get product URL
     * 3. Generate AI-enhanced content with product URL
     * 4. Apply platform-specific formatting
     */
    public function executeProductFirstWorkflow(array $data): array
    {
        try {
            $result = [
                'success' => false,
                'message' => '',
                'data' => [
                    'product_data' => null,
                    'content' => [],
                    'meta' => []
                ]
            ];

            // Step 1: Create product in Nazmart if requested
            $productUrl = null;
            $productData = null;

            if ($data['post_to_website_first'] ?? false) {
                $productResult = $this->createProductInNazmart($data);

                if (!$productResult['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create product: ' . $productResult['message'],
                        'data' => $productResult['data'] ?? []
                    ];
                }

                $productData = $productResult['data'];
                $productUrl = $productData['product_url'] ?? null;
            } else {
                // Generate placeholder URL for preview
                $productUrl = $this->generatePlaceholderUrl($data);
            }

            // Step 2: Generate AI-enhanced content
            $contentResult = $this->generateEnhancedContent($data, $productUrl);

            if (!$contentResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to generate content: ' . $contentResult['message'],
                    'data' => []
                ];
            }

            // Step 3: Format content for platforms
            $platformContent = $this->formatContentForPlatforms(
                $contentResult['data'],
                $data['target_platforms'] ?? [],
                $productUrl
            );

            $result['success'] = true;
            $result['message'] = 'Product-first workflow completed successfully!';
            $result['data'] = [
                'product_data' => $productData,
                'content' => $platformContent,
                'meta' => [
                    'product_url' => $productUrl,
                    'created_product' => $data['post_to_website_first'] ?? false,
                    'platforms' => $data['target_platforms'] ?? [],
                    'workflow_type' => 'product_first'
                ]
            ];

            return $result;

        } catch (\Exception $e) {
            Log::error('Product-first workflow error: ' . $e->getMessage(), [
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred during the product-first workflow: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Create product in Nazmart
     */
    private function createProductInNazmart(array $data): array
    {
        try {
            $productData = [
                'product_name' => $data['product_name'],
                'price' => $data['product_price'] ?? 0,
                'category' => $data['product_category'] ?? 'general',
                'description' => $data['product_description'],
                'image' => $data['product_image'] ?? null,
                'vendor_id' => auth_user()->nazmart_store_id
            ];

            return $this->productUploadService->createProduct($productData);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create product in Nazmart: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Generate AI-enhanced content with product integration
     */
    private function generateEnhancedContent(array $data, string $productUrl): array
    {
        try {
            // Create request object for AI service
            $request = new Request([
                'product_name' => $data['product_name'],
                'product_description' => $data['product_description'],
                'product_url' => $productUrl,
                'product_price' => $data['product_price'] ?? null,
                'target_platforms' => $data['target_platforms'] ?? [],
                'language' => $data['language'] ?? 'English',
                'tone' => $data['tone'] ?? 'Professional',
                'include_hashtags' => $data['include_hashtags'] ?? true,
                'include_emojis' => $data['include_emojis'] ?? true,
                'include_cta' => $data['include_cta'] ?? true,
            ]);

            $aiResult = $this->aiService->generateProductEnhancedContent($request);

            if (!$aiResult['status']) {
                return [
                    'success' => false,
                    'message' => $aiResult['message'] ?? 'Failed to generate AI content',
                    'data' => []
                ];
            }

            // Parse AI response
            $parsedContent = $this->parseAiResponse($aiResult['data'] ?? '');

            return [
                'success' => true,
                'message' => 'AI content generated successfully',
                'data' => $parsedContent
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate AI content: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Parse AI response (handle JSON or plain text)
     */
    private function parseAiResponse(string $response): array
    {
        // Try to decode as JSON first
        $decoded = json_decode($response, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // Fallback to plain text parsing
        return [
            'caption' => $response,
            'hashtags' => $this->extractHashtags($response),
            'meta_description' => substr($response, 0, 160),
            'keywords' => $this->extractKeywords($response),
            'platform_variations' => []
        ];
    }

    /**
     * Extract hashtags from text
     */
    private function extractHashtags(string $text): array
    {
        preg_match_all('/#\w+/', $text, $matches);
        return array_unique($matches[0] ?? []);
    }

    /**
     * Extract keywords from text
     */
    private function extractKeywords(string $text): array
    {
        // Simple keyword extraction - remove common words and get unique words
        $commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'];

        $words = str_word_count(strtolower($text), 1);
        $keywords = array_diff($words, $commonWords);
        $keywords = array_filter($keywords, function ($word) {
            return strlen($word) > 3;
        });

        return array_values(array_unique(array_slice($keywords, 0, 10)));
    }

    /**
     * Format content for specific platforms
     */
    private function formatContentForPlatforms(array $content, array $platforms, string $productUrl): array
    {
        $formatted = [];
        $baseCaption = $content['caption'] ?? '';
        $hashtags = $content['hashtags'] ?? [];
        $platformVariations = $content['platform_variations'] ?? [];

        foreach ($platforms as $platform) {
            $platformCaption = $platformVariations[$platform] ?? $baseCaption;

            switch ($platform) {
                case 'instagram':
                    $formatted[$platform] = $this->formatForInstagram($platformCaption, $hashtags, $productUrl);
                    break;

                case 'facebook':
                    $formatted[$platform] = $this->formatForFacebook($platformCaption, $hashtags, $productUrl);
                    break;

                case 'whatsapp':
                    $formatted[$platform] = $this->formatForWhatsApp($platformCaption, $hashtags, $productUrl);
                    break;

                case 'twitter':
                    $formatted[$platform] = $this->formatForTwitter($platformCaption, $hashtags, $productUrl);
                    break;

                default:
                    $formatted[$platform] = [
                        'caption' => $platformCaption,
                        'hashtags' => $hashtags,
                        'product_url' => $productUrl,
                        'url_handling' => 'direct'
                    ];
            }
        }

        return $formatted;
    }

    /**
     * Format content for Instagram
     */
    private function formatForInstagram(string $caption, array $hashtags, string $productUrl): array
    {
        return [
            'caption' => $caption,
            'hashtags' => $hashtags,
            'product_url' => $productUrl,
            'url_handling' => 'bio_link',
            'note' => 'Instagram posts do not support clickable URLs. Use "Link in bio" reference. URLs work in Stories and Reels.',
            'tagged_url' => $productUrl
        ];
    }

    /**
     * Format content for Facebook
     */
    private function formatForFacebook(string $caption, array $hashtags, string $productUrl): array
    {
        $formattedCaption = $caption;
        if ($productUrl) {
            $formattedCaption .= "\n\n🛒 Shop now: " . $productUrl;
        }

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 5),
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Format content for WhatsApp
     */
    private function formatForWhatsApp(string $caption, array $hashtags, string $productUrl): array
    {
        $formattedCaption = $caption;
        if ($productUrl) {
            $formattedCaption .= "\n\n🛍️ Get yours here: " . $productUrl;
        }

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 3),
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Format content for Twitter
     */
    private function formatForTwitter(string $caption, array $hashtags, string $productUrl): array
    {
        $availableChars = 280;
        if ($productUrl) {
            $availableChars -= (strlen($productUrl) + 1);
        }

        $hashtagText = implode(' ', array_slice($hashtags, 0, 3));
        $availableChars -= strlen($hashtagText);

        $truncatedCaption = strlen($caption) > $availableChars
            ? substr($caption, 0, $availableChars - 3) . '...'
            : $caption;

        $formattedCaption = $truncatedCaption;
        if ($productUrl) {
            $formattedCaption .= " " . $productUrl;
        }
        $formattedCaption .= " " . $hashtagText;

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 3),
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Generate placeholder URL for preview purposes
     */
    private function generatePlaceholderUrl(array $data): string
    {
        $storeName = auth_user()->nazmart_store_slug ?? 'your-store';
        $productSlug = \Str::slug($data['product_name'] ?? 'sample-product');
        return "https://vendora.com/stores/{$storeName}/products/{$productSlug}";
    }
}
