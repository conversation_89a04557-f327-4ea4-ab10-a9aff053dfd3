import 'package:flutter/foundation.dart';

enum AiAssistantStatus { initial, loading, loaded, generating, error }

class AiMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;

  AiMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
  });
}

class AiAssistantProvider extends ChangeNotifier {
  AiAssistantStatus _status = AiAssistantStatus.initial;
  List<AiMessage> _messages = [];
  String? _errorMessage;

  AiAssistantStatus get status => _status;
  List<AiMessage> get messages => _messages;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == AiAssistantStatus.loading;
  bool get isGenerating => _status == AiAssistantStatus.generating;

  void initializeChat() {
    if (_messages.isEmpty) {
      _messages.add(AiMessage(
        id: '1',
        content: 'Hello! I\'m your AI assistant. I can help you create engaging social media posts, optimize your content, and manage your store. How can I assist you today?',
        isUser: false,
        timestamp: DateTime.now(),
      ));
      _setStatus(AiAssistantStatus.loaded);
    }
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    // Add user message
    final userMessage = AiMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
    );
    _messages.add(userMessage);
    notifyListeners();

    _setStatus(AiAssistantStatus.generating);

    try {
      // TODO: Implement actual AI API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock AI response based on user input
      String aiResponse = _generateMockResponse(content);

      final aiMessage = AiMessage(
        id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );
      _messages.add(aiMessage);
      _setStatus(AiAssistantStatus.loaded);
    } catch (e) {
      _setError('Failed to get AI response');
    }
  }

  Future<void> generatePostContent({
    required String topic,
    required List<String> platforms,
    String? tone,
  }) async {
    _setStatus(AiAssistantStatus.generating);

    try {
      // TODO: Implement actual AI API call
      await Future.delayed(const Duration(seconds: 3));

      String generatedContent = _generatePostContent(topic, platforms, tone);

      final aiMessage = AiMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: 'Here\'s a generated post for "$topic":\n\n$generatedContent\n\nWould you like me to modify anything or generate alternatives?',
        isUser: false,
        timestamp: DateTime.now(),
      );
      _messages.add(aiMessage);
      _setStatus(AiAssistantStatus.loaded);
    } catch (e) {
      _setError('Failed to generate post content');
    }
  }

  void clearChat() {
    _messages.clear();
    initializeChat();
  }

  String _generateMockResponse(String userInput) {
    final input = userInput.toLowerCase();
    
    if (input.contains('post') || input.contains('content')) {
      return 'I can help you create engaging social media posts! What topic would you like to post about? I can generate content optimized for different platforms like Instagram, Facebook, Twitter, and more.';
    } else if (input.contains('store') || input.contains('product')) {
      return 'I can assist with your store management! I can help you write product descriptions, create promotional content, or generate ideas for showcasing your products on social media.';
    } else if (input.contains('analytics') || input.contains('performance')) {
      return 'I can help you understand your social media performance and suggest improvements. Would you like tips on increasing engagement or optimizing your posting schedule?';
    } else if (input.contains('help') || input.contains('what can you do')) {
      return 'I can help you with:\n\n• Creating engaging social media posts\n• Writing product descriptions\n• Generating hashtags\n• Optimizing content for different platforms\n• Analyzing post performance\n• Store management tips\n\nWhat would you like to work on?';
    } else {
      return 'That\'s interesting! I\'m here to help you with social media content creation and store management. Could you tell me more about what you\'d like to accomplish?';
    }
  }

  String _generatePostContent(String topic, List<String> platforms, String? tone) {
    final toneText = tone ?? 'professional';
    final platformText = platforms.join(', ');
    
    return '🌟 Exciting news about $topic! \n\nWe\'re thrilled to share this amazing update with our community. This represents our commitment to bringing you the best experience possible.\n\n✨ Key highlights:\n• Innovation at its finest\n• Quality you can trust\n• Community-focused approach\n\n#$topic #Innovation #Quality #Community\n\n(Optimized for: $platformText with a $toneText tone)';
  }

  void _setStatus(AiAssistantStatus status) {
    _status = status;
    if (status != AiAssistantStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(AiAssistantStatus.error);
  }
}
