<?php

namespace App\Http\Services\External;

use App\Http\Services\CurlService;
use Illuminate\Support\Facades\Log;

class StoreCreationService
{
    private $nazmart_api_url;
    private $api_key;

    public function __construct()
    {
        $this->nazmart_api_url = config('settings.social_platforms.nazmart.credential.api_url', 'http://localhost/nazmart/api');
        $this->api_key = config('settings.social_platforms.nazmart.credential.api_key', '');
    }

    /**
     * Create a new store in Nazmart
     *
     * @param array $storeData
     * @return array
     */
    public function createStore(array $storeData): array
    {
        try {
            // Validate required fields
            $requiredFields = ['name', 'email', 'store_name', 'phone'];
            foreach ($requiredFields as $field) {
                if (empty($storeData[$field])) {
                    return [
                        'success' => false,
                        'message' => "Field '{$field}' is required",
                        'data' => null
                    ];
                }
            }

            // Prepare API endpoint
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/create-store';

            // Prepare headers
            $headers = [
                'Content-Type: application/json',
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            // Prepare data payload
            $payload = [
                'name' => $storeData['name'],
                'email' => $storeData['email'],
                'store_name' => $storeData['store_name'],
                'phone' => $storeData['phone'],
                'category' => $storeData['category'] ?? null
            ];

            // Log the request for debugging
            Log::info('Nazmart Store Creation Request', [
                'endpoint' => $endpoint,
                'payload' => $payload
            ]);

            // Make the API call
            $response = CurlService::curlPostRequestWithHeadersJson($endpoint, $headers, $payload);

            // Log the response for debugging
            Log::info('Nazmart Store Creation Response', [
                'response' => $response
            ]);

            // Parse response
            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to connect to Nazmart API',
                    'data' => null
                ];
            }

            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from Nazmart API',
                    'data' => null
                ];
            }

            // Check if the API call was successful
            if (isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => $responseData['message'] ?? 'Store created successfully',
                    'data' => $responseData['data'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Failed to create store',
                    'data' => $responseData['errors'] ?? null
                ];
            }

        } catch (\Exception $e) {
            Log::error('Nazmart Store Creation Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the store: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Test connection to Nazmart API
     *
     * @return array
     */
    public function testConnection(): array
    {
        try {
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/health';

            $headers = [
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            $response = CurlService::curlGetRequestWithHeaders($endpoint, $headers);

            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Cannot connect to Nazmart API'
                ];
            }

            return [
                'success' => true,
                'message' => 'Connection to Nazmart API successful'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get store information from Nazmart
     *
     * @param string $storeId
     * @return array
     */
    public function getStoreInfo(string $storeId): array
    {
        try {
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/store/' . $storeId;

            $headers = [
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            $response = CurlService::curlGetRequestWithHeaders($endpoint, $headers);

            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch store information',
                    'data' => null
                ];
            }

            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from Nazmart API',
                    'data' => null
                ];
            }

            return [
                'success' => isset($responseData['success']) ? $responseData['success'] : false,
                'message' => $responseData['message'] ?? 'Store information retrieved',
                'data' => $responseData['data'] ?? null
            ];

        } catch (\Exception $e) {
            Log::error('Nazmart Get Store Info Error', [
                'store_id' => $storeId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while fetching store information: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
