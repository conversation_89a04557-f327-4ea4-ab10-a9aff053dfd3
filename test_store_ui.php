<?php
/**
 * Test script to verify store link UI functionality
 * This script simulates the post creation page with store links
 */

// Simulate user data with and without store
$testUsers = [
    'user_with_store' => [
        'nazmart_store_id' => 'store_123',
        'nazmart_store_slug' => 'test-store',
        'nazmart_store_url' => 'https://vendora.com/stores/test-store',
        'nazmart_store_created_at' => '2024-01-15 10:30:00',
    ],
    'user_without_store' => [
        'nazmart_store_id' => null,
        'nazmart_store_slug' => null,
        'nazmart_store_url' => null,
        'nazmart_store_created_at' => null,
    ]
];

// Simulate accounts data
$testAccounts = [
    [
        'id' => 1,
        'name' => 'My Instagram',
        'platform' => ['name' => 'Instagram', 'slug' => 'instagram'],
        'account_information' => ['avatar' => 'https://via.placeholder.com/40']
    ],
    [
        'id' => 2,
        'name' => 'My WhatsApp',
        'platform' => ['name' => 'WhatsApp', 'slug' => 'whatsapp'],
        'account_information' => ['avatar' => 'https://via.placeholder.com/40']
    ],
    [
        'id' => 3,
        'name' => 'My Facebook',
        'platform' => ['name' => 'Facebook', 'slug' => 'facebook'],
        'account_information' => ['avatar' => 'https://via.placeholder.com/40']
    ],
    [
        'id' => 4,
        'name' => 'My Twitter',
        'platform' => ['name' => 'Twitter', 'slug' => 'twitter'],
        'account_information' => ['avatar' => 'https://via.placeholder.com/40']
    ]
];

function testStoreUIFunctionality() {
    global $testUsers, $testAccounts;
    
    echo "=== Store Link UI Functionality Test ===\n\n";
    
    // Test 1: User with store
    echo "Test 1: User with Nazmart store\n";
    echo "Store Slug: " . $testUsers['user_with_store']['nazmart_store_slug'] . "\n";
    echo "Store URL: " . $testUsers['user_with_store']['nazmart_store_url'] . "\n";
    echo "Expected: Store link icons should be enabled for Instagram, WhatsApp, Facebook\n";
    echo "Expected: Store modal should show store information and visit/copy buttons\n\n";
    
    // Test 2: User without store
    echo "Test 2: User without Nazmart store\n";
    echo "Store Slug: " . ($testUsers['user_without_store']['nazmart_store_slug'] ?: 'null') . "\n";
    echo "Expected: Store link icons should be disabled for all platforms\n";
    echo "Expected: Store modal should show 'No Store Connected' message\n\n";
    
    // Test 3: Platform filtering
    echo "Test 3: Platform filtering for store links\n";
    foreach ($testAccounts as $account) {
        $platformSlug = strtolower($account['platform']['slug']);
        $showStoreLink = in_array($platformSlug, ['instagram', 'whatsapp', 'facebook']);
        echo "Platform: {$account['platform']['name']} ({$platformSlug}) - ";
        echo "Show Store Link: " . ($showStoreLink ? 'YES' : 'NO') . "\n";
    }
    echo "\n";
    
    // Test 4: URL generation
    echo "Test 4: Store URL generation\n";
    $storeSlug = $testUsers['user_with_store']['nazmart_store_slug'];
    $expectedUrl = "https://vendora.com/stores/{$storeSlug}";
    echo "Store Slug: {$storeSlug}\n";
    echo "Generated URL: {$expectedUrl}\n";
    echo "Expected: URL should match the pattern https://vendora.com/stores/{slug}\n\n";
    
    echo "=== Test Summary ===\n";
    echo "✓ Store link icons should appear next to Instagram, WhatsApp, Facebook checkboxes\n";
    echo "✓ Store link icons should be enabled only when user has a store\n";
    echo "✓ Clicking store link should open modal with store information\n";
    echo "✓ Modal should show different content based on store availability\n";
    echo "✓ Store URL should follow the pattern: https://vendora.com/stores/{slug}\n";
    echo "✓ Copy functionality should work for store URLs\n\n";
    
    return true;
}

// Run the test
if (php_sapi_name() === 'cli') {
    testStoreUIFunctionality();
} else {
    echo "<pre>";
    testStoreUIFunctionality();
    echo "</pre>";
}

echo "Test completed successfully!\n";
echo "To verify the implementation:\n";
echo "1. Navigate to the post creation page in BeePost\n";
echo "2. Check that store link icons appear next to social media checkboxes\n";
echo "3. Test the modal functionality by clicking the store link icons\n";
echo "4. Verify the copy URL functionality works\n";
echo "5. Test with both users who have stores and users who don't\n";
?>
