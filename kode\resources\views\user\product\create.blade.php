@extends('layouts.master')
@section('content')

@push('style-include')
    <link nonce="{{ csp_nonce() }}" href="{{asset('assets/frontend/css/post.css')}}" rel="stylesheet" type="text/css">
    <style>
        .product-form-container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .image-upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .post-after-upload {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: #007bff;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
@endpush

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="i-card-md">
                <div class="card-header">
                    <h4 class="card-title">
                        {{translate('Create Product')}}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="product-form-container">
                        
                        <!-- Store Information -->
                        <div class="alert alert-info">
                            <strong>{{translate('Store')}}:</strong> 
                            <a href="{{$user_store['url']}}" target="_blank">{{$user_store['slug']}}</a>
                        </div>

                        <!-- Product Creation Form -->
                        <form id="productForm" enctype="multipart/form-data">
                            @csrf
                            
                            <!-- Product Name -->
                            <div class="form-group">
                                <label class="form-label" for="product_name">{{translate('Product Name')}} *</label>
                                <input type="text" id="product_name" name="product_name" class="form-control" 
                                       placeholder="{{translate('Enter product name')}}" required>
                            </div>

                            <!-- Price -->
                            <div class="form-group">
                                <label class="form-label" for="price">{{translate('Price')}} *</label>
                                <input type="number" id="price" name="price" class="form-control" 
                                       placeholder="{{translate('Enter price')}}" step="0.01" min="0" required>
                            </div>

                            <!-- Category -->
                            <div class="form-group">
                                <label class="form-label" for="category">{{translate('Category')}} *</label>
                                <select id="category" name="category" class="form-control" required>
                                    <option value="">{{translate('Select Category')}}</option>
                                    @foreach($categories as $category)
                                        <option value="{{$category['id']}}">{{$category['name']}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Description -->
                            <div class="form-group">
                                <label class="form-label" for="description">{{translate('Description')}} *</label>
                                <textarea id="description" name="description" class="form-control" rows="4" 
                                          placeholder="{{translate('Enter product description')}}" required></textarea>
                            </div>

                            <!-- Image Upload -->
                            <div class="form-group">
                                <label class="form-label">{{translate('Product Image')}}</label>
                                <div class="image-upload-area" id="imageUploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">{{translate('Click to upload or drag and drop')}}</p>
                                    <p class="text-muted small">{{translate('Supported formats: JPG, PNG, GIF (Max: 2MB)')}}</p>
                                    <input type="file" id="image" name="image" accept="image/*" style="display: none;">
                                </div>
                                <div id="imagePreview" style="display: none;">
                                    <img id="previewImg" class="image-preview" alt="Preview">
                                    <button type="button" class="btn btn-sm btn-danger mt-2" id="removeImage">
                                        {{translate('Remove Image')}}
                                    </button>
                                </div>
                            </div>

                            <!-- Post After Upload Option -->
                            <div class="post-after-upload">
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" id="post_after_upload" name="post_after_upload" value="1">
                                    <label for="post_after_upload" class="form-label">
                                        {{translate('Post this product to social media after creation')}}
                                    </label>
                                </div>
                                <p class="text-muted small mt-2">
                                    {{translate('If checked, you will be redirected to the post scheduler with this product pre-filled.')}}
                                </p>
                            </div>

                            <!-- Form Actions -->
                            <div class="form-group mt-4">
                                <button type="button" class="btn-secondary" onclick="window.history.back()">
                                    {{translate('Cancel')}}
                                </button>
                                <button type="submit" class="btn-primary" id="submitBtn">
                                    {{translate('Create Product')}}
                                </button>
                            </div>

                        </form>

                        <!-- Loading Overlay -->
                        <div id="loadingOverlay" style="display: none;">
                            <div class="text-center">
                                <div class="spinner"></div>
                                <p class="mt-2">{{translate('Creating product...')}}</p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('script-include')
<script nonce="{{ csp_nonce() }}">
$(document).ready(function() {
    // Image upload handling
    $('#imageUploadArea').on('click', function() {
        $('#image').click();
    });

    $('#image').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#imageUploadArea').hide();
            };
            reader.readAsDataURL(file);
        }
    });

    $('#removeImage').on('click', function() {
        $('#image').val('');
        $('#imagePreview').hide();
        $('#imageUploadArea').show();
    });

    // Drag and drop functionality
    $('#imageUploadArea').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('#imageUploadArea').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('#imageUploadArea').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#image')[0].files = files;
            $('#image').trigger('change');
        }
    });

    // Form submission
    $('#productForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        // Show loading state
        $('#loadingOverlay').show();
        $('.product-form-container').addClass('loading');
        $('#submitBtn').prop('disabled', true);
        
        $.ajax({
            url: '{{route("user.product.store")}}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    if (response.redirect_to_post && response.product_url) {
                        // Redirect to post creation with product data
                        const postUrl = '{{route("user.post.create")}}' + 
                                       '?product_url=' + encodeURIComponent(response.product_url) +
                                       '&product_name=' + encodeURIComponent(response.data.name);
                        window.location.href = postUrl;
                    } else {
                        // Show success message and redirect to product list
                        showAlert('success', response.message);
                        setTimeout(function() {
                            window.location.href = '{{route("user.product.index")}}';
                        }, 2000);
                    }
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr) {
                let message = 'An error occurred while creating the product.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert('danger', message);
            },
            complete: function() {
                // Hide loading state
                $('#loadingOverlay').hide();
                $('.product-form-container').removeClass('loading');
                $('#submitBtn').prop('disabled', false);
            }
        });
    });

    function showAlert(type, message) {
        const alertHtml = `<div class="alert alert-${type}">${message}</div>`;
        $('.product-form-container').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
@endpush

@endsection
