import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../providers/create_post_provider.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_overlay.dart';
import '../../widgets/media_preview_widget.dart';
import '../../widgets/platform_checkbox.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _captionController = TextEditingController();
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CreatePostProvider>().resetForm();
    });
  }

  @override
  void dispose() {
    _captionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Post'),
        elevation: 0,
        actions: [
          Consumer<CreatePostProvider>(
            builder: (context, provider, child) {
              return TextButton(
                onPressed: provider.canPost ? () => _handlePost(provider) : null,
                child: Text(
                  'Post',
                  style: TextStyle(
                    color: provider.canPost
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<CreatePostProvider>(
        builder: (context, provider, child) {
          return LoadingOverlay(
            isLoading: provider.isPosting,
            loadingText: 'Publishing your post...',
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Caption Input
                  CustomTextField(
                    controller: _captionController,
                    label: 'Caption',
                    hint: 'What\'s on your mind?',
                    maxLines: 4,
                    onChanged: provider.updateCaption,
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Media Section
                  _buildMediaSection(provider),
                  
                  const SizedBox(height: 24),
                  
                  // Platform Selection
                  _buildPlatformSelection(provider),
                  
                  const SizedBox(height: 24),
                  
                  // Preview Section
                  if (provider.hasContent) _buildPreviewSection(provider),
                  
                  const SizedBox(height: 24),
                  
                  // Error Message
                  if (provider.errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.error.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        provider.errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ),
                  
                  // Post Button
                  CustomButton(
                    text: 'Publish Post',
                    onPressed: provider.canPost ? () => _handlePost(provider) : null,
                    isLoading: provider.isPosting,
                  ),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMediaSection(CreatePostProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Media',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Media Upload Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Photo',
                    icon: Icons.photo_camera,
                    variant: ButtonVariant.outline,
                    onPressed: () => _pickImage(provider),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'Video',
                    icon: Icons.videocam,
                    variant: ButtonVariant.outline,
                    onPressed: () => _pickVideo(provider),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'Gallery',
                    icon: Icons.photo_library,
                    variant: ButtonVariant.outline,
                    onPressed: () => _pickFromGallery(provider),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Media Preview
            if (provider.mediaFiles.isNotEmpty)
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: provider.mediaFiles.length,
                  itemBuilder: (context, index) {
                    final media = provider.mediaFiles[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: MediaPreviewWidget(
                        media: media,
                        onRemove: () => provider.removeMedia(index),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlatformSelection(CreatePostProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Post to Platforms',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            PlatformCheckbox(
              platform: 'WhatsApp',
              icon: Icons.chat,
              color: const Color(0xFF25D366),
              isSelected: provider.selectedPlatforms.contains('whatsapp'),
              onChanged: (selected) => provider.togglePlatform('whatsapp'),
              subtitle: 'Post as WhatsApp Status',
            ),
            
            const SizedBox(height: 12),
            
            PlatformCheckbox(
              platform: 'Instagram',
              icon: Icons.camera_alt,
              color: const Color(0xFFE4405F),
              isSelected: provider.selectedPlatforms.contains('instagram'),
              onChanged: (selected) => provider.togglePlatform('instagram'),
              subtitle: 'Share to Instagram feed',
            ),
            
            const SizedBox(height: 12),
            
            PlatformCheckbox(
              platform: 'Facebook',
              icon: Icons.facebook,
              color: const Color(0xFF1877F2),
              isSelected: provider.selectedPlatforms.contains('facebook'),
              onChanged: (selected) => provider.togglePlatform('facebook'),
              subtitle: 'Post to Facebook page',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(CreatePostProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // WhatsApp Status Preview (if WhatsApp is selected)
            if (provider.selectedPlatforms.contains('whatsapp'))
              _buildWhatsAppPreview(provider),
            
            // General Social Media Preview
            if (provider.selectedPlatforms.any((p) => p != 'whatsapp'))
              _buildSocialMediaPreview(provider),
          ],
        ),
      ),
    );
  }

  Widget _buildWhatsAppPreview(CreatePostProvider provider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF25D366).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF25D366).withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.chat,
                color: const Color(0xFF25D366),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'WhatsApp Status Preview',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: const Color(0xFF25D366),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (provider.mediaFiles.isNotEmpty)
            Container(
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[200],
              ),
              child: provider.mediaFiles.first.type == 'image'
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        File(provider.mediaFiles.first.path),
                        fit: BoxFit.cover,
                      ),
                    )
                  : const Center(
                      child: Icon(Icons.play_circle_outline, size: 40),
                    ),
            ),
          if (provider.caption.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              provider.caption,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSocialMediaPreview(CreatePostProvider provider) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Your Business',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (provider.caption.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              provider.caption,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
          if (provider.mediaFiles.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: provider.mediaFiles.length,
                itemBuilder: (context, index) {
                  final media = provider.mediaFiles[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: media.type == 'image'
                          ? Image.file(
                              File(media.path),
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.play_circle_outline,
                                size: 40,
                              ),
                            ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _pickImage(CreatePostProvider provider) async {
    final status = await Permission.camera.request();
    if (status.isGranted) {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: ImageSource.camera);
      if (image != null) {
        provider.addMedia(image.path, 'image');
      }
    }
  }

  Future<void> _pickVideo(CreatePostProvider provider) async {
    final status = await Permission.camera.request();
    if (status.isGranted) {
      final picker = ImagePicker();
      final video = await picker.pickVideo(source: ImageSource.camera);
      if (video != null) {
        provider.addMedia(video.path, 'video');
      }
    }
  }

  Future<void> _pickFromGallery(CreatePostProvider provider) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.media,
      allowMultiple: true,
    );
    
    if (result != null) {
      for (final file in result.files) {
        if (file.path != null) {
          final type = file.extension?.toLowerCase() == 'mp4' ||
                  file.extension?.toLowerCase() == 'mov' ||
                  file.extension?.toLowerCase() == 'avi'
              ? 'video'
              : 'image';
          provider.addMedia(file.path!, type);
        }
      }
    }
  }

  Future<void> _handlePost(CreatePostProvider provider) async {
    final success = await provider.publishPost();
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Post published successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    }
  }
}
