<?php

namespace App\Services\Nazmart;

/**
 * Nazmart Product Upload Service
 * 
 * Handles all product creation, upload, and management operations with Nazmart API
 * including product creation, image uploads, category management, and product updates.
 */
class ProductUploadService extends BaseNazmartService
{
    /**
     * Create a new product in Nazmart store
     *
     * @param array $productData
     * @return array
     */
    public function createProduct(array $productData): array
    {
        // Validate required fields
        $validation = $this->validateRequiredFields($productData, [
            'product_name', 'price', 'category', 'description', 'vendor_id'
        ]);

        if ($validation) {
            return $validation;
        }

        // Prepare data payload
        $payload = [
            'product_name' => $productData['product_name'],
            'price' => $productData['price'],
            'category' => $productData['category'],
            'description' => $productData['description'],
            'image' => $productData['image'] ?? null,
            'vendor_id' => $productData['vendor_id']
        ];

        return $this->makePostRequest('products', $payload);
    }

    /**
     * Update an existing product
     *
     * @param string $productId
     * @param array $productData
     * @return array
     */
    public function updateProduct(string $productId, array $productData): array
    {
        if (empty($productId)) {
            return [
                'success' => false,
                'message' => 'Product ID is required',
                'data' => null,
                'error_code' => 'MISSING_PRODUCT_ID'
            ];
        }

        return $this->makePostRequest("products/{$productId}", $productData);
    }

    /**
     * Upload image to Nazmart store
     *
     * @param string $storeId
     * @param mixed $imageFile
     * @return array
     */
    public function uploadImage(string $storeId, $imageFile): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        if (empty($imageFile)) {
            return [
                'success' => false,
                'message' => 'Image file is required',
                'data' => null,
                'error_code' => 'MISSING_IMAGE_FILE'
            ];
        }

        // Prepare file data
        $postData = [
            'store_id' => $storeId,
            'image' => $imageFile
        ];

        return $this->makeFileUploadRequest('upload-image', $postData);
    }

    /**
     * Upload multiple images for a product
     *
     * @param string $storeId
     * @param array $imageFiles
     * @return array
     */
    public function uploadMultipleImages(string $storeId, array $imageFiles): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        if (empty($imageFiles)) {
            return [
                'success' => false,
                'message' => 'At least one image file is required',
                'data' => null,
                'error_code' => 'MISSING_IMAGE_FILES'
            ];
        }

        $uploadResults = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($imageFiles as $index => $imageFile) {
            $result = $this->uploadImage($storeId, $imageFile);
            $uploadResults[] = [
                'index' => $index,
                'result' => $result
            ];

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'message' => "Uploaded {$successCount} images successfully, {$failureCount} failed",
            'data' => [
                'results' => $uploadResults,
                'summary' => [
                    'total' => count($imageFiles),
                    'success' => $successCount,
                    'failed' => $failureCount
                ]
            ]
        ];
    }

    /**
     * Get categories from Nazmart store
     *
     * @param string $storeId
     * @return array
     */
    public function getCategories(string $storeId): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => [],
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        $payload = ['store_id' => $storeId];
        return $this->makePostRequest('get-categories', $payload);
    }

    /**
     * Get product information
     *
     * @param string $productId
     * @return array
     */
    public function getProduct(string $productId): array
    {
        if (empty($productId)) {
            return [
                'success' => false,
                'message' => 'Product ID is required',
                'data' => null,
                'error_code' => 'MISSING_PRODUCT_ID'
            ];
        }

        return $this->makeGetRequest("products/{$productId}");
    }

    /**
     * Get products by store
     *
     * @param string $storeId
     * @param array $filters
     * @return array
     */
    public function getProductsByStore(string $storeId, array $filters = []): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => [],
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        $params = array_merge(['store_id' => $storeId], $filters);
        return $this->makeGetRequest('products', $params);
    }

    /**
     * Delete a product
     *
     * @param string $productId
     * @return array
     */
    public function deleteProduct(string $productId): array
    {
        if (empty($productId)) {
            return [
                'success' => false,
                'message' => 'Product ID is required',
                'data' => null,
                'error_code' => 'MISSING_PRODUCT_ID'
            ];
        }

        return $this->makePostRequest("products/{$productId}/delete", []);
    }

    /**
     * Validate product data before creation/update
     *
     * @param array $productData
     * @param bool $isUpdate
     * @return array
     */
    public function validateProductData(array $productData, bool $isUpdate = false): array
    {
        $errors = [];

        // Required fields for creation
        if (!$isUpdate) {
            $requiredFields = ['product_name', 'price', 'category', 'description', 'vendor_id'];
            foreach ($requiredFields as $field) {
                if (empty($productData[$field])) {
                    $errors[$field] = "The {$field} field is required.";
                }
            }
        }

        // Price validation
        if (isset($productData['price'])) {
            if (!is_numeric($productData['price']) || $productData['price'] < 0) {
                $errors['price'] = 'The price must be a valid positive number.';
            }
        }

        // Product name validation
        if (isset($productData['product_name'])) {
            if (strlen($productData['product_name']) < 3) {
                $errors['product_name'] = 'The product name must be at least 3 characters.';
            }
            if (strlen($productData['product_name']) > 191) {
                $errors['product_name'] = 'The product name may not be greater than 191 characters.';
            }
        }

        // Description validation
        if (isset($productData['description'])) {
            if (strlen($productData['description']) < 10) {
                $errors['description'] = 'The description must be at least 10 characters.';
            }
        }

        if (!empty($errors)) {
            return [
                'success' => false,
                'message' => 'Validation failed',
                'data' => null,
                'errors' => $errors,
                'error_code' => 'VALIDATION_FAILED'
            ];
        }

        return [
            'success' => true,
            'message' => 'Validation passed',
            'data' => $productData
        ];
    }

    /**
     * Bulk create products
     *
     * @param array $productsData Array of product data arrays
     * @return array
     */
    public function bulkCreateProducts(array $productsData): array
    {
        if (empty($productsData)) {
            return [
                'success' => false,
                'message' => 'No product data provided',
                'data' => null,
                'error_code' => 'EMPTY_PRODUCTS_DATA'
            ];
        }

        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($productsData as $index => $productData) {
            $result = $this->createProduct($productData);
            $results[] = [
                'index' => $index,
                'product_name' => $productData['product_name'] ?? "Product {$index}",
                'result' => $result
            ];

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'message' => "Created {$successCount} products successfully, {$failureCount} failed",
            'data' => [
                'results' => $results,
                'summary' => [
                    'total' => count($productsData),
                    'success' => $successCount,
                    'failed' => $failureCount
                ]
            ]
        ];
    }
}
