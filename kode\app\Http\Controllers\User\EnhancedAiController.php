<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Services\AiService;
use App\Services\Nazmart\ProductUploadService;
use App\Http\Services\ProductFirstWorkflowService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class EnhancedAiController extends Controller
{
    protected $aiService;
    protected $nazmartProductService;
    protected $productFirstWorkflowService;

    public function __construct(
        AiService $aiService,
        ProductUploadService $nazmartProductService,
        ProductFirstWorkflowService $productFirstWorkflowService
    ) {
        $this->aiService = $aiService;
        $this->nazmartProductService = $nazmartProductService;
        $this->productFirstWorkflowService = $productFirstWorkflowService;
    }

    /**
     * Generate product-enhanced content with product-first workflow
     */
    public function generateProductContent(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'product_name' => 'required|string|max:255',
                'product_description' => 'required|string',
                'product_price' => 'nullable|numeric|min:0',
                'product_category' => 'nullable|string',
                'product_image' => 'nullable|string',
                'target_platforms' => 'nullable|array',
                'target_platforms.*' => 'string',
                'post_to_website_first' => 'nullable|boolean',
                'language' => 'nullable|string|max:50',
                'tone' => 'nullable|string|max:50',
                'include_hashtags' => 'nullable|boolean',
                'include_emojis' => 'nullable|boolean',
                'include_cta' => 'nullable|boolean',
            ]);

            // Use the ProductFirstWorkflowService for complete workflow
            $workflowData = [
                'product_name' => $request->input('product_name'),
                'product_description' => $request->input('product_description'),
                'product_price' => $request->input('product_price'),
                'product_category' => $request->input('product_category'),
                'product_image' => $request->input('product_image'),
                'target_platforms' => $request->input('target_platforms', []),
                'post_to_website_first' => $request->boolean('post_to_website_first'),
                'language' => $request->input('language', 'English'),
                'tone' => $request->input('tone', 'Professional'),
                'include_hashtags' => $request->boolean('include_hashtags', true),
                'include_emojis' => $request->boolean('include_emojis', true),
                'include_cta' => $request->boolean('include_cta', true),
            ];

            $result = $this->productFirstWorkflowService->executeProductFirstWorkflow($workflowData);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'data' => $result['data'] ?? []
                ], 400);
            }

            // Store product data in session for post creation if product was created
            if ($result['data']['product_data']) {
                session([
                    'product_for_posting' => array_merge($result['data']['product_data'], [
                        'use_enhanced_ai' => true,
                        'created_at' => now()
                    ])
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Enhanced AI product content generation error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating content: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Generate standard content for non-product posts
     */
    public function generateStandardContent(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'post_content' => 'required|string',
                'target_platforms' => 'required|array',
                'language' => 'nullable|string',
                'tone' => 'nullable|string',
                'include_hashtags' => 'nullable|boolean',
                'include_emojis' => 'nullable|boolean',
            ]);

            $aiResult = $this->aiService->generateStandardContent($request);

            if (!$aiResult['status']) {
                return response()->json([
                    'success' => false,
                    'message' => $aiResult['message'] ?? 'Failed to generate content',
                    'errors' => []
                ], 400);
            }

            $generatedContent = $this->parseAiResponse($aiResult['data'] ?? '');

            $platformContent = $this->formatForPlatforms(
                $generatedContent,
                $request->input('target_platforms')
            );

            return response()->json([
                'success' => true,
                'message' => 'Content generated successfully',
                'data' => [
                    'content' => $platformContent,
                    'meta' => [
                        'platforms' => $request->input('target_platforms'),
                        'content_type' => 'standard'
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating content: ' . $e->getMessage(),
                'errors' => []
            ], 500);
        }
    }

    /**
     * Create product in Nazmart
     */
    private function createProductInNazmart(Request $request): array
    {
        try {
            $productData = [
                'product_name' => $request->input('product_name'),
                'price' => $request->input('product_price'),
                'category' => $request->input('product_category'),
                'description' => $request->input('product_description'),
                'image' => $request->input('product_image'),
                'vendor_id' => auth_user()->nazmart_store_id
            ];

            return $this->nazmartProductService->createProduct($productData);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create product in Nazmart: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Parse AI response (handle JSON or plain text)
     */
    private function parseAiResponse(string $response): array
    {
        // Try to decode as JSON first
        $decoded = json_decode($response, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // Fallback to plain text parsing
        return [
            'caption' => $response,
            'hashtags' => $this->extractHashtags($response),
            'platform_variations' => []
        ];
    }

    /**
     * Extract hashtags from text
     */
    private function extractHashtags(string $text): array
    {
        preg_match_all('/#\w+/', $text, $matches);
        return array_unique($matches[0] ?? []);
    }

    /**
     * Format content for specific platforms
     */
    private function formatForPlatforms(array $content, array $platforms, ?string $productUrl = null): array
    {
        $formatted = [];
        $baseCaption = $content['caption'] ?? '';
        $hashtags = $content['hashtags'] ?? [];
        $platformVariations = $content['platform_variations'] ?? [];

        foreach ($platforms as $platform) {
            $platformCaption = $platformVariations[$platform] ?? $baseCaption;

            switch ($platform) {
                case 'instagram':
                    $formatted[$platform] = $this->formatForInstagram($platformCaption, $hashtags, $productUrl);
                    break;

                case 'facebook':
                    $formatted[$platform] = $this->formatForFacebook($platformCaption, $hashtags, $productUrl);
                    break;

                case 'whatsapp':
                    $formatted[$platform] = $this->formatForWhatsApp($platformCaption, $hashtags, $productUrl);
                    break;

                case 'twitter':
                    $formatted[$platform] = $this->formatForTwitter($platformCaption, $hashtags, $productUrl);
                    break;

                default:
                    $formatted[$platform] = [
                        'caption' => $platformCaption,
                        'hashtags' => $hashtags,
                        'product_url' => $productUrl,
                        'url_handling' => 'direct'
                    ];
            }
        }

        return $formatted;
    }

    /**
     * Format content for Instagram
     */
    private function formatForInstagram(string $caption, array $hashtags, ?string $productUrl): array
    {
        return [
            'caption' => $caption,
            'hashtags' => $hashtags,
            'product_url' => $productUrl,
            'url_handling' => 'bio_link', // URLs don't work in posts, use bio link reference
            'note' => 'Instagram posts do not support clickable URLs. Use "Link in bio" reference. URLs work in Stories and Reels.',
            'tagged_url' => $productUrl // For tagging purposes
        ];
    }

    /**
     * Format content for Facebook
     */
    private function formatForFacebook(string $caption, array $hashtags, ?string $productUrl): array
    {
        $formattedCaption = $caption;
        if ($productUrl) {
            $formattedCaption .= "\n\n🛒 Shop now: " . $productUrl;
        }

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 5), // Facebook works better with fewer hashtags
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Format content for WhatsApp
     */
    private function formatForWhatsApp(string $caption, array $hashtags, ?string $productUrl): array
    {
        $formattedCaption = $caption;
        if ($productUrl) {
            $formattedCaption .= "\n\n🛍️ Get yours here: " . $productUrl;
        }

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 3), // Minimal hashtags for WhatsApp
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Format content for Twitter
     */
    private function formatForTwitter(string $caption, array $hashtags, ?string $productUrl): array
    {
        // Twitter has character limits, so we need to be concise
        $availableChars = 280;
        if ($productUrl) {
            $availableChars -= (strlen($productUrl) + 1); // URL + space
        }

        $hashtagText = implode(' ', array_slice($hashtags, 0, 3));
        $availableChars -= strlen($hashtagText);

        $truncatedCaption = strlen($caption) > $availableChars
            ? substr($caption, 0, $availableChars - 3) . '...'
            : $caption;

        $formattedCaption = $truncatedCaption;
        if ($productUrl) {
            $formattedCaption .= " " . $productUrl;
        }
        $formattedCaption .= " " . $hashtagText;

        return [
            'caption' => $formattedCaption,
            'hashtags' => array_slice($hashtags, 0, 3),
            'product_url' => $productUrl,
            'url_handling' => 'direct'
        ];
    }

    /**
     * Generate placeholder URL for preview purposes
     */
    private function generatePlaceholderUrl(Request $request): string
    {
        $storeName = auth_user()->nazmart_store_slug ?? 'your-store';
        $productSlug = \Str::slug($request->input('product_name'));
        return "https://vendora.com/stores/{$storeName}/products/{$productSlug}";
    }
}
