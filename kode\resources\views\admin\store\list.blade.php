@extends('admin.layouts.master')

@section('content')
    <div class="row g-4">
        <div class="col-xl-12">
            <div class="i-card-md">
                <div class="card--header">
                    <h4 class="card-title">
                        {{translate('Manage Stores')}}
                    </h4>
                    <div class="action">
                        <a href="{{route('admin.store.create')}}" class="i-btn btn--sm success">
                            <i class="las la-plus"></i> {{translate('Create New Store')}}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>{{translate('Store Management')}}</h6>
                        <p class="mb-0">{{translate('This section allows you to create and manage stores in your connected Nazmart installation. Each store will be created as a separate tenant with its own subdomain and database.')}}</p>
                    </div>

                    <div class="row mb-4">
                        <div class="col-lg-6">
                            <div class="form-inner">
                                <label for="storeIdInput">
                                    {{translate('Get Store Information')}}
                                </label>
                                <div class="input-group">
                                    <input type="text" 
                                           id="storeIdInput" 
                                           class="form-control" 
                                           placeholder="{{translate('Enter Store ID or Subdomain')}}">
                                    <button class="btn btn-primary" type="button" id="getStoreInfoBtn">
                                        <i class="las la-search"></i> {{translate('Get Info')}}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-inner">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="button" id="testConnectionBtn" class="btn btn-success">
                                        <i class="las la-plug"></i> {{translate('Test API Connection')}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Store Information Display -->
                    <div id="storeInfoSection" style="display: none;">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">{{translate('Store Information')}}</h6>
                            </div>
                            <div class="card-body" id="storeInfoContent">
                                <!-- Content will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Section -->
                    <div class="mt-4">
                        <h5>{{translate('API Configuration')}}</h5>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>{{translate('Nazmart API Settings')}}</h6>
                                        <p class="text-muted">{{translate('Configure your Nazmart API connection in the platform settings.')}}</p>
                                        <div class="mb-2">
                                            <strong>{{translate('API URL:')}}</strong> 
                                            <span class="text-muted">{{config('settings.social_platforms.nazmart.credential.api_url', 'Not configured')}}</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>{{translate('API Key:')}}</strong> 
                                            <span class="text-muted">
                                                @if(config('settings.social_platforms.nazmart.credential.api_key'))
                                                    {{translate('Configured')}}
                                                @else
                                                    {{translate('Not configured')}}
                                                @endif
                                            </span>
                                        </div>
                                        <a href="{{route('admin.platform.list')}}" class="btn btn-sm btn-outline-primary">
                                            <i class="las la-cog"></i> {{translate('Configure API')}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>{{translate('Quick Actions')}}</h6>
                                        <p class="text-muted">{{translate('Common store management actions.')}}</p>
                                        <div class="d-grid gap-2">
                                            <a href="{{route('admin.store.create')}}" class="btn btn-primary">
                                                <i class="las la-plus"></i> {{translate('Create New Store')}}
                                            </a>
                                            <button type="button" class="btn btn-outline-info" id="refreshConnectionBtn">
                                                <i class="las la-sync"></i> {{translate('Refresh Connection')}}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-4">
                        <div class="alert alert-light">
                            <h6>{{translate('How to use Store Management:')}}</h6>
                            <ul class="mb-0">
                                <li>{{translate('Ensure your Nazmart API is properly configured in Platform Settings')}}</li>
                                <li>{{translate('Use "Create New Store" to add new stores to your Nazmart installation')}}</li>
                                <li>{{translate('Each store gets its own subdomain and database automatically')}}</li>
                                <li>{{translate('Use "Get Store Information" to check details of existing stores')}}</li>
                                <li>{{translate('Test the API connection regularly to ensure everything is working')}}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-include')
<script nonce="{{ csp_nonce() }}">
    $(document).ready(function() {
        // Test connection functionality
        $('#testConnectionBtn, #refreshConnectionBtn').click(function() {
            const btn = $(this);
            const originalText = btn.html();
            
            btn.html('<i class="las la-spinner la-spin"></i> {{translate("Testing...")}}').prop('disabled', true);
            
            $.ajax({
                url: '{{route("admin.store.test.connection")}}',
                method: 'POST',
                data: {
                    _token: '{{csrf_token()}}'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    toastr.error(response?.message || '{{translate("Connection test failed")}}');
                },
                complete: function() {
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });

        // Get store information functionality
        $('#getStoreInfoBtn').click(function() {
            const storeId = $('#storeIdInput').val().trim();
            
            if (!storeId) {
                toastr.error('{{translate("Please enter a Store ID or Subdomain")}}');
                return;
            }
            
            const btn = $(this);
            const originalText = btn.html();
            
            btn.html('<i class="las la-spinner la-spin"></i> {{translate("Loading...")}}').prop('disabled', true);
            
            $.ajax({
                url: '{{route("admin.store.get.info")}}',
                method: 'POST',
                data: {
                    _token: '{{csrf_token()}}',
                    store_id: storeId
                },
                success: function(response) {
                    if (response.success && response.data) {
                        displayStoreInfo(response.data);
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message || '{{translate("Store not found")}}');
                        $('#storeInfoSection').hide();
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    toastr.error(response?.message || '{{translate("Failed to get store information")}}');
                    $('#storeInfoSection').hide();
                },
                complete: function() {
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });

        // Allow Enter key to trigger store info search
        $('#storeIdInput').keypress(function(e) {
            if (e.which === 13) {
                $('#getStoreInfoBtn').click();
            }
        });
    });

    function displayStoreInfo(storeData) {
        let content = '<div class="row">';
        
        if (storeData.store_id) {
            content += '<div class="col-md-6"><strong>{{translate("Store ID:")}} </strong>' + storeData.store_id + '</div>';
        }
        if (storeData.subdomain) {
            content += '<div class="col-md-6"><strong>{{translate("Subdomain:")}} </strong>' + storeData.subdomain + '</div>';
        }
        if (storeData.owner_name) {
            content += '<div class="col-md-6"><strong>{{translate("Owner:")}} </strong>' + storeData.owner_name + '</div>';
        }
        if (storeData.email) {
            content += '<div class="col-md-6"><strong>{{translate("Email:")}} </strong>' + storeData.email + '</div>';
        }
        if (storeData.phone) {
            content += '<div class="col-md-6"><strong>{{translate("Phone:")}} </strong>' + storeData.phone + '</div>';
        }
        if (storeData.category) {
            content += '<div class="col-md-6"><strong>{{translate("Category:")}} </strong>' + storeData.category + '</div>';
        }
        if (storeData.status) {
            content += '<div class="col-md-6"><strong>{{translate("Status:")}} </strong><span class="badge bg-' + (storeData.status === 'active' ? 'success' : 'warning') + '">' + storeData.status + '</span></div>';
        }
        if (storeData.created_at) {
            content += '<div class="col-md-6"><strong>{{translate("Created:")}} </strong>' + storeData.created_at + '</div>';
        }
        
        content += '</div>';
        
        if (storeData.store_url || storeData.admin_url) {
            content += '<hr><div class="row">';
            if (storeData.store_url) {
                content += '<div class="col-md-6"><a href="' + storeData.store_url + '" target="_blank" class="btn btn-sm btn-outline-primary"><i class="las la-external-link-alt"></i> {{translate("Visit Store")}}</a></div>';
            }
            if (storeData.admin_url) {
                content += '<div class="col-md-6"><a href="' + storeData.admin_url + '" target="_blank" class="btn btn-sm btn-outline-success"><i class="las la-cog"></i> {{translate("Admin Panel")}}</a></div>';
            }
            content += '</div>';
        }
        
        $('#storeInfoContent').html(content);
        $('#storeInfoSection').show();
    }
</script>
@endpush
