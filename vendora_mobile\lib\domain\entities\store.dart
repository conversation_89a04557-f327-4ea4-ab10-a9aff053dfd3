import 'package:equatable/equatable.dart';

enum StoreStatus { active, inactive, pending, suspended }

class Store extends Equatable {
  final String id;
  final String userId;
  final String name;
  final String slug;
  final String? description;
  final String? logo;
  final String? banner;
  final StoreStatus status;
  final StoreSettings settings;
  final List<Product> products;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Store({
    required this.id,
    required this.userId,
    required this.name,
    required this.slug,
    this.description,
    this.logo,
    this.banner,
    required this.status,
    required this.settings,
    required this.products,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        slug,
        description,
        logo,
        banner,
        status,
        settings,
        products,
        createdAt,
        updatedAt,
      ];
}

class StoreSettings extends Equatable {
  final String currency;
  final bool isPublic;
  final bool allowOrders;
  final Map<String, dynamic> socialLinks;
  final Map<String, dynamic> paymentMethods;

  const StoreSettings({
    required this.currency,
    required this.isPublic,
    required this.allowOrders,
    required this.socialLinks,
    required this.paymentMethods,
  });

  @override
  List<Object?> get props => [
        currency,
        isPublic,
        allowOrders,
        socialLinks,
        paymentMethods,
      ];
}

class Product extends Equatable {
  final String id;
  final String storeId;
  final String name;
  final String description;
  final double price;
  final String? image;
  final List<String> gallery;
  final String category;
  final int stock;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Product({
    required this.id,
    required this.storeId,
    required this.name,
    required this.description,
    required this.price,
    this.image,
    required this.gallery,
    required this.category,
    required this.stock,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        storeId,
        name,
        description,
        price,
        image,
        gallery,
        category,
        stock,
        isActive,
        createdAt,
        updatedAt,
      ];
}
