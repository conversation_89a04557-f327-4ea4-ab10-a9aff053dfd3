<?php

namespace App\Services\Nazmart;

use App\Http\Services\CurlService;
use Illuminate\Support\Facades\Log;

/**
 * Base service class for all Nazmart API integrations
 * 
 * This class provides common functionality for all Nazmart services including:
 * - API configuration management
 * - Common HTTP request methods
 * - Response parsing and error handling
 * - Logging and debugging utilities
 */
abstract class BaseNazmartService
{
    protected $nazmart_api_url;
    protected $api_key;
    protected $service_name;

    public function __construct()
    {
        $this->nazmart_api_url = config('settings.social_platforms.nazmart.credential.api_url', 'http://localhost/nazmart/api');
        $this->api_key = config('settings.social_platforms.nazmart.credential.api_key', '');
        $this->service_name = class_basename(static::class);
    }

    /**
     * Make a GET request to Nazmart API
     *
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    protected function makeGetRequest(string $endpoint, array $params = []): array
    {
        try {
            $url = rtrim($this->nazmart_api_url, '/') . '/' . ltrim($endpoint, '/');
            
            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }

            $headers = $this->buildHeaders();

            $this->logRequest('GET', $url, $params);

            $response = CurlService::curlGetRequestWithHeaders($url, $headers);

            return $this->parseResponse($response, 'GET', $endpoint);

        } catch (\Exception $e) {
            return $this->handleException($e, 'GET', $endpoint);
        }
    }

    /**
     * Make a POST request to Nazmart API
     *
     * @param string $endpoint
     * @param array $data
     * @param bool $isJson
     * @return array
     */
    protected function makePostRequest(string $endpoint, array $data = [], bool $isJson = true): array
    {
        try {
            $url = rtrim($this->nazmart_api_url, '/') . '/' . ltrim($endpoint, '/');
            $headers = $this->buildHeaders($isJson);

            $this->logRequest('POST', $url, $data);

            if ($isJson) {
                $response = CurlService::curlPostRequestWithHeadersJson($url, $headers, $data);
            } else {
                $response = CurlService::curlPostRequestWithHeaders($url, $headers, $data);
            }

            return $this->parseResponse($response, 'POST', $endpoint);

        } catch (\Exception $e) {
            return $this->handleException($e, 'POST', $endpoint);
        }
    }

    /**
     * Make a POST request with file upload to Nazmart API
     *
     * @param string $endpoint
     * @param array $data
     * @return array
     */
    protected function makeFileUploadRequest(string $endpoint, array $data = []): array
    {
        try {
            $url = rtrim($this->nazmart_api_url, '/') . '/' . ltrim($endpoint, '/');
            $headers = $this->buildHeaders(false, true);

            $this->logRequest('POST_FILE', $url, array_keys($data));

            $response = CurlService::curlPostRequestWithFile($url, $headers, $data);

            return $this->parseResponse($response, 'POST_FILE', $endpoint);

        } catch (\Exception $e) {
            return $this->handleException($e, 'POST_FILE', $endpoint);
        }
    }

    /**
     * Build headers for API requests
     *
     * @param bool $isJson
     * @param bool $isFileUpload
     * @return array
     */
    protected function buildHeaders(bool $isJson = true, bool $isFileUpload = false): array
    {
        $headers = [];

        if ($isJson && !$isFileUpload) {
            $headers[] = 'Content-Type: application/json';
        }

        $headers[] = 'Accept: application/json';

        if (!empty($this->api_key)) {
            $headers[] = 'Authorization: Bearer ' . $this->api_key;
        }

        return $headers;
    }

    /**
     * Parse API response
     *
     * @param mixed $response
     * @param string $method
     * @param string $endpoint
     * @return array
     */
    protected function parseResponse($response, string $method, string $endpoint): array
    {
        $this->logResponse($method, $endpoint, $response);

        if ($response === false) {
            return [
                'success' => false,
                'message' => 'Failed to connect to Nazmart API',
                'data' => null,
                'error_code' => 'CONNECTION_FAILED'
            ];
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'Invalid response from Nazmart API',
                'data' => null,
                'error_code' => 'INVALID_RESPONSE',
                'raw_response' => $response
            ];
        }

        // Standardize response format
        if (isset($responseData['success'])) {
            return [
                'success' => $responseData['success'],
                'message' => $responseData['message'] ?? ($responseData['success'] ? 'Operation successful' : 'Operation failed'),
                'data' => $responseData['data'] ?? null,
                'errors' => $responseData['errors'] ?? null
            ];
        }

        // Handle legacy response formats
        return [
            'success' => true,
            'message' => 'Operation completed',
            'data' => $responseData
        ];
    }

    /**
     * Handle exceptions
     *
     * @param \Exception $e
     * @param string $method
     * @param string $endpoint
     * @return array
     */
    protected function handleException(\Exception $e, string $method, string $endpoint): array
    {
        $errorMessage = "Nazmart API {$method} request to {$endpoint} failed: " . $e->getMessage();
        
        Log::error($errorMessage, [
            'service' => $this->service_name,
            'method' => $method,
            'endpoint' => $endpoint,
            'exception' => $e->getTraceAsString()
        ]);

        return [
            'success' => false,
            'message' => $errorMessage,
            'data' => null,
            'error_code' => 'EXCEPTION',
            'exception_type' => get_class($e)
        ];
    }

    /**
     * Log API request
     *
     * @param string $method
     * @param string $url
     * @param array $data
     */
    protected function logRequest(string $method, string $url, array $data): void
    {
        Log::info("Nazmart API {$method} Request", [
            'service' => $this->service_name,
            'method' => $method,
            'url' => $url,
            'data' => $method === 'POST_FILE' ? $data : $data // Don't log file contents
        ]);
    }

    /**
     * Log API response
     *
     * @param string $method
     * @param string $endpoint
     * @param mixed $response
     */
    protected function logResponse(string $method, string $endpoint, $response): void
    {
        Log::info("Nazmart API {$method} Response", [
            'service' => $this->service_name,
            'method' => $method,
            'endpoint' => $endpoint,
            'response_length' => is_string($response) ? strlen($response) : 'non-string',
            'response_preview' => is_string($response) ? substr($response, 0, 200) : $response
        ]);
    }

    /**
     * Validate required fields
     *
     * @param array $data
     * @param array $requiredFields
     * @return array|null Returns error array if validation fails, null if successful
     */
    protected function validateRequiredFields(array $data, array $requiredFields): ?array
    {
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return [
                    'success' => false,
                    'message' => "Field '{$field}' is required",
                    'data' => null,
                    'error_code' => 'VALIDATION_FAILED',
                    'missing_field' => $field
                ];
            }
        }

        return null;
    }

    /**
     * Test connection to Nazmart API
     *
     * @return array
     */
    public function testConnection(): array
    {
        return $this->makeGetRequest('health');
    }
}
