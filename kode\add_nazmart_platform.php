<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Support\Str;

// Database configuration - update these with your actual database credentials
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'beepost_db', // Update with your actual database name
    'username' => 'root',       // Update with your actual username
    'password' => '',           // Update with your actual password
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Check if Nazmart platform already exists
    $existing = Capsule::table('media_platforms')
        ->where('name', 'Nazmart')
        ->orWhere('slug', 'nazmart')
        ->first();

    if ($existing) {
        echo "Nazmart platform already exists with ID: " . $existing->id . "\n";
        
        // Update the configuration
        Capsule::table('media_platforms')
            ->where('id', $existing->id)
            ->update([
                'configuration' => json_encode([
                    'api_url' => 'http://localhost/nazmart/api',  // Update with your Nazmart URL
                    'api_key' => 'your-api-key-here'             // Update with your API key
                ]),
                'is_integrated' => 1,
                'updated_at' => now()
            ]);
        
        echo "Updated Nazmart platform configuration.\n";
    } else {
        // Create new Nazmart platform
        $platformId = Capsule::table('media_platforms')->insertGetId([
            'uid' => Str::uuid(),
            'name' => 'Nazmart',
            'slug' => 'nazmart',
            'description' => 'Nazmart Store Creation Integration',
            'url' => 'https://nazmart.com',
            'configuration' => json_encode([
                'api_url' => 'http://localhost/nazmart/api',  // Update with your Nazmart URL
                'api_key' => 'your-api-key-here'             // Update with your API key
            ]),
            'status' => 1,
            'is_integrated' => 1,
            'is_feature' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        echo "Created Nazmart platform with ID: " . $platformId . "\n";
    }

    echo "Nazmart platform setup completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Update the API URL and API key in the platform configuration\n";
    echo "2. Access the BeePost admin panel\n";
    echo "3. Go to Platforms section to configure Nazmart credentials\n";
    echo "4. Test the API connection\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
}

function now() {
    return date('Y-m-d H:i:s');
}
