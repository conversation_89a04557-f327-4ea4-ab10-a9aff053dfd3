@extends('layouts.master')
@section('content')

@push('style-include')
    <style>
        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .btn-create {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
        }

        .btn-create:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }

        .store-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .store-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 600;
        }

        .store-link:hover {
            text-decoration: underline;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .empty-state i {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            color: #495057;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .product-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .product-content {
            padding: 15px;
        }

        .product-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }

        .product-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.875rem;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            color: white;
        }

        .btn-outline-secondary {
            background: transparent;
            color: #6c757d;
            border: 1px solid #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
        }

        .loading-spinner {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
@endpush

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="i-card-md">
                <div class="card-header">
                    <div class="product-header">
                        <h4 class="card-title">
                            {{translate('My Products')}}
                        </h4>
                        <a href="{{route('user.product.create')}}" class="btn-create">
                            <i class="fas fa-plus"></i> {{translate('Create Product')}}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    
                    <!-- Store Information -->
                    <div class="store-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{translate('Your Store')}}:</strong>
                                <a href="{{$user_store['url']}}" target="_blank" class="store-link">
                                    {{$user_store['slug']}}
                                </a>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <small class="text-muted">
                                    {{translate('Store ID')}}: {{$user_store['id']}}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Products Container -->
                    <div id="productsContainer">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <p class="mt-3">{{translate('Loading products...')}}</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@push('script-include')
<script nonce="{{ csp_nonce() }}">
$(document).ready(function() {
    loadProducts();

    function loadProducts() {
        // For now, show empty state since we haven't implemented product listing API
        showEmptyState();
    }

    function showEmptyState() {
        const emptyStateHtml = `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <h3>{{translate('No Products Yet')}}</h3>
                <p>{{translate('You haven\'t created any products yet. Start by creating your first product!')}}</p>
                <a href="{{route('user.product.create')}}" class="btn-create">
                    <i class="fas fa-plus"></i> {{translate('Create Your First Product')}}
                </a>
            </div>
        `;
        $('#productsContainer').html(emptyStateHtml);
    }

    function showProducts(products) {
        if (products.length === 0) {
            showEmptyState();
            return;
        }

        let productsHtml = '<div class="product-grid">';
        
        products.forEach(function(product) {
            productsHtml += `
                <div class="product-card">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}" class="product-image">` :
                        `<div class="product-image d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>`
                    }
                    <div class="product-content">
                        <h5 class="product-title">${product.name}</h5>
                        <div class="product-price">$${parseFloat(product.price).toFixed(2)}</div>
                        <p class="product-description">${product.description}</p>
                        <div class="product-actions">
                            <a href="${product.product_url}" target="_blank" class="btn btn-sm btn-primary">
                                <i class="fas fa-external-link-alt"></i> {{translate('View')}}
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="createPostWithProduct('${product.product_url}', '${product.name}')">
                                <i class="fas fa-share"></i> {{translate('Post')}}
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        productsHtml += '</div>';
        $('#productsContainer').html(productsHtml);
    }

    // Function to create a post with product
    window.createPostWithProduct = function(productUrl, productName) {
        const postCreateUrl = '{{route("user.post.create")}}' + 
                             '?product_url=' + encodeURIComponent(productUrl) +
                             '&product_name=' + encodeURIComponent(productName);
        window.location.href = postCreateUrl;
    };
});
</script>
@endpush

@endsection
