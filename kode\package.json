{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "socket-server": "node --experimental-modules resources/js/socket-server.js"}, "devDependencies": {"axios": "^1.4.0", "laravel-vite-plugin": "^0.7.5", "vite": "^4.0.0", "@vitejs/plugin-vue": "^4.2.3", "vue": "^3.3.4"}, "dependencies": {"@sentry/browser": "^7.40.0", "dotenv": "^16.3.1", "express": "^4.18.2", "ioredis": "^5.3.2", "redis": "^4.6.7", "redis-server": "^1.2.2", "save": "^2.9.0", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2"}}