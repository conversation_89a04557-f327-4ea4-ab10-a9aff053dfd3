<?php
// Store verification script
header('Content-Type: text/plain');

echo "=== Nazmart Store Verification ===\n\n";

try {
    // Database connection for Nazmart
    $pdo = new PDO('mysql:host=localhost;dbname=nazmart_db', 'root', ''); // Update credentials
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. Checking Nazmart database connection... ✓\n";
    
    // Check if tenants table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tenants");
    $tenantCount = $stmt->fetch()['count'];
    echo "2. Total tenants in database: $tenantCount\n";
    
    // Get recent tenants
    $stmt = $pdo->query("SELECT id, data FROM tenants ORDER BY created_at DESC LIMIT 5");
    $tenants = $stmt->fetchAll();
    
    echo "3. Recent tenants:\n";
    foreach ($tenants as $tenant) {
        $data = json_decode($tenant['data'], true);
        $subdomain = $data['tenancy_db_name'] ?? 'unknown';
        echo "   - Tenant ID: {$tenant['id']}, Subdomain: $subdomain\n";
    }
    
    // Check users table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "4. Total users in central database: $userCount\n";
    
    // Get recent users
    $stmt = $pdo->query("SELECT id, name, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    $users = $stmt->fetchAll();
    
    echo "5. Recent users:\n";
    foreach ($users as $user) {
        echo "   - User: {$user['name']} ({$user['email']}) - {$user['created_at']}\n";
    }
    
    echo "\n=== BeePost Database Check ===\n";
    
    // Check BeePost database
    $pdo2 = new PDO('mysql:host=localhost;dbname=beepost_db', 'root', ''); // Update credentials
    $pdo2->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "6. Checking BeePost database connection... ✓\n";
    
    // Check if Nazmart platform exists
    $stmt = $pdo2->query("SELECT * FROM media_platforms WHERE name = 'Nazmart' OR slug = 'nazmart'");
    $nazmart = $stmt->fetch();
    
    if ($nazmart) {
        echo "7. Nazmart platform found in BeePost:\n";
        echo "   - ID: {$nazmart['id']}\n";
        echo "   - Name: {$nazmart['name']}\n";
        echo "   - Status: {$nazmart['status']}\n";
        echo "   - Integrated: {$nazmart['is_integrated']}\n";
        echo "   - Configuration: {$nazmart['configuration']}\n";
    } else {
        echo "7. Nazmart platform NOT found in BeePost ❌\n";
    }
    
} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
    echo "Please update the database credentials in this script.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== Manual Verification Steps ===\n";
echo "1. Check if teststore123.localhost resolves\n";
echo "2. Visit http://teststore123.localhost (if configured)\n";
echo "3. Check Nazmart admin for new tenant\n";
echo "4. Verify email was sent to test user\n";
echo "\nVerification completed.\n";
?>
