<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Nazmart\StoreCreationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class StoreController extends Controller
{
    private $storeService;

    public function __construct(StoreCreationService $storeService)
    {
        $this->storeService = $storeService;
    }

    /**
     * Display the store creation form
     */
    public function create()
    {
        return view('admin.store.create');
    }

    /**
     * Handle store creation request
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'store_name' => 'required|string|max:255|regex:/^[a-zA-Z0-9\-_]+$/',
            'phone' => 'required|string|max:20',
            'category' => 'nullable|string|max:255'
        ], [
            'store_name.regex' => 'Store name can only contain letters, numbers, hyphens, and underscores'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Prepare store data
            $storeData = [
                'name' => $request->name,
                'email' => $request->email,
                'store_name' => $request->store_name,
                'phone' => $request->phone,
                'category' => $request->category
            ];

            // Create store via API
            $result = $this->storeService->createStore($storeData);

            if ($result['success']) {
                // Update current user with store information
                $user = auth_user();
                if ($user && isset($result['data'])) {
                    $storeInfo = $result['data'];
                    $user->update([
                        'nazmart_store_id' => $storeInfo['store_id'] ?? null,
                        'nazmart_store_slug' => $storeInfo['store_slug'] ?? null,
                        'nazmart_store_url' => $storeInfo['store_url'] ?? null,
                        'nazmart_store_created_at' => now(),
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'errors' => $result['data']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the store: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display list of created stores
     */
    public function list()
    {
        // For now, we'll just show the list view
        // In the future, this could fetch stores from a local database
        // that tracks created stores
        return view('admin.store.list');
    }

    /**
     * Test connection to Nazmart API
     */
    public function testConnection()
    {
        try {
            $result = $this->storeService->testConnection();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get store information
     */
    public function getStoreInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Store ID is required',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->storeService->getStoreInfo($request->store_id);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get store information: ' . $e->getMessage()
            ], 500);
        }
    }
}
