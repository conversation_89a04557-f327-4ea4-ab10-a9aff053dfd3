<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\StoreCreationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Store Creation API for BeePost Integration
Route::post('/create-store', [StoreCreationController::class, 'createStore'])
    ->middleware(['api'])
    ->name('api.create-store');

Route::post('/get-store-info', [StoreCreationController::class, 'getStoreInfo'])
    ->middleware(['api'])
    ->name('api.get-store-info');

// Product Management API for BeePost Integration
Route::post('/create-product', [App\Http\Controllers\Api\ProductManagementController::class, 'createProduct'])
    ->middleware(['api'])
    ->name('api.create-product');

// Simplified Product Creation API for BeePost
Route::post('/products', [App\Http\Controllers\Api\ProductManagementController::class, 'createSimpleProduct'])
    ->middleware(['api'])
    ->name('api.products.create');

Route::post('/get-products', [App\Http\Controllers\Api\ProductManagementController::class, 'getProducts'])
    ->middleware(['api'])
    ->name('api.get-products');

Route::put('/update-product/{productId}', [App\Http\Controllers\Api\ProductManagementController::class, 'updateProduct'])
    ->middleware(['api'])
    ->name('api.update-product');

Route::delete('/delete-product/{productId}', [App\Http\Controllers\Api\ProductManagementController::class, 'deleteProduct'])
    ->middleware(['api'])
    ->name('api.delete-product');

Route::post('/get-product/{productId}', [App\Http\Controllers\Api\ProductManagementController::class, 'getProduct'])
    ->middleware(['api'])
    ->name('api.get-product');

// Store Categories and Attributes API
Route::post('/get-categories', [App\Http\Controllers\Api\ProductManagementController::class, 'getCategories'])
    ->middleware(['api'])
    ->name('api.get-categories');

Route::post('/get-brands', [App\Http\Controllers\Api\ProductManagementController::class, 'getBrands'])
    ->middleware(['api'])
    ->name('api.get-brands');

Route::post('/get-units', [App\Http\Controllers\Api\ProductManagementController::class, 'getUnits'])
    ->middleware(['api'])
    ->name('api.get-units');
