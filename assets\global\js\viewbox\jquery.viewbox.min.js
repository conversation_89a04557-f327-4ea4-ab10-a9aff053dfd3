!function(q){q.fn.viewbox=function(f){void 0===f&&(f={}),f=q.extend({template:'<div class="viewbox-container"><div class="viewbox-body"><div class="viewbox-header"></div><div class="viewbox-content"></div><div class="viewbox-footer"></div></div></div>',loader:'<div class="loader"><div class="spinner"><div class="double-bounce1"></div><div class="double-bounce2"></div></div></div>',setTitle:!0,margin:20,resizeDuration:400,openDuration:200,closeDuration:200,closeButton:!0,fullscreenButton:!1,navButtons:!0,closeOnSideClick:!0,nextOnContentClick:!1,useGestures:!0,imageExt:["png","jpg","jpeg","webp","gif"]},f);var o,n,e,c,l,s,i=q(this),d=q(f.template),t=q(f.loader),u=!1,p=!1,r=[];function a(e){var t,n,i;p||(function(){var e;for(;r.length;)(e=r.shift()).placeholder.before(e.content),e.placeholder.detach()}(),t=e.attr("href"),n=f.setTitle?e.data("viewbox-title")||e.attr("title"):"",t?h(t)?(o=e,function(e,t,n){var l=q('<img class="viewbox-image" alt="">').attr("src",e);k(l)||z(!0);y("header",t),y("content",""),y("footer",n);var s=m("body"),d=0,u=m("content"),v=m("header"),h=m("footer"),g=Boolean("string"===jQuery.type(t)&&t.length),w=Boolean("string"===jQuery.type(n)&&n.length);v.toggle(g),h.toggle(w),x();var b=window.setInterval(function(){var e,t,n,i,o,r,a,c;!k(l)&&d<1e3?d++:(window.clearInterval(b),z(!1),q("body").append(l),e=s.width()-u.width()+2*f.margin,t=s.height()-u.height()+2*f.margin,n=g?v.outerHeight():0,i=w?h.outerHeight():0,o=q(window).width()-e,r=q(window).height()-t-n-i,a=l.width(),c=l.height(),l.detach(),o<a&&(c=c*o/a,a=o),r<c&&(a=a*r/c,c=r),p=!0,v.add(h).animate({width:a},f.resizeDuration),u.animate({width:a,height:c},f.resizeDuration),s.animate({"margin-left":-(a+e)/2+f.margin,"margin-top":-(c+t)/2+f.margin},f.resizeDuration,function(){u.append(l),p=!1}))},k(l)?0:200)}(t,n)):(i=t).match(/^#.+$/i)&&q(i).length&&(o=e,w(t,n)):w(o=e,n))}function x(e,t){var n,i,o=m("body"),r=m("content"),a=m("header"),c=m("footer");e?(r.width(e),a.width(e),c.width(e)):u||(a.width(1),c.width(1)),t&&r.height(t),u?(n=o.width(),i=o.height()):(u=!0,q("body").append(d),d.show(),n=o.width(),i=o.height(),d.hide(),d.fadeIn(f.openDuration)),o.css({"margin-left":-n/2,"margin-top":-i/2})}function m(e){return d.find(".viewbox-"+e)}function y(e,t){m(e).html(t)}function v(){var t=-1;return o&&i.each(function(e){if(o.is(this))return t=e,!1}),t}function h(e){return e&&q.isArray(f.imageExt)&&f.imageExt.length&&e.match(new RegExp("("+f.imageExt.join("|")+")(\\?.*)?$","i"))}function k(e){return e.get(0).complete}function z(e){e?t.appendTo(m("body")):t.detach()}function g(e){var t=q('<div class="viewbox-button-default viewbox-button-'+e+'"></div>'),n=window.location.pathname+window.location.search+"#viewbox-"+e+"-icon";return t.appendTo(d).get(0).insertAdjacentHTML("afterbegin",'<svg><use xlink:href="'+n+'"/></svg>'),t}function w(e,t){var n=q(e),i=q('<div class="viewbox-content-placeholder"></div>');n.before(i),u&&d.trigger("viewbox.close"),y("content",""),y("header",t),m("content").append(n),x("auto","auto"),r.push({placeholder:i,content:n})}return q("#viewbox-sprite").length||q("body").get(0).insertAdjacentHTML("afterbegin",'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="viewbox-sprite" style="display:none"><symbol id="viewbox-close-icon" viewBox="0 0 50 50"><path d="M37.304 11.282l1.414 1.414-26.022 26.02-1.414-1.413z"/><path d="M12.696 11.282l26.022 26.02-1.414 1.415-26.022-26.02z"/></symbol><symbol id="viewbox-prev-icon" viewBox="0 0 50 50"><path d="M27.3 34.7L17.6 25l9.7-9.7 1.4 1.4-8.3 8.3 8.3 8.3z"/></symbol><symbol id="viewbox-next-icon" viewBox="0 0 50 50"><path d="M22.7 34.7l-1.4-1.4 8.3-8.3-8.3-8.3 1.4-1.4 9.7 9.7z"/></symbol><symbol id="viewbox-full-screen-icon" viewBox="0 0 50 50"><path d="M8.242 11.387v9.037h2.053v-6.986h9.197v-2.051H8.242zm22.606 0v2.05h9.367v6.987h2.05v-9.037H30.849zM8.242 29.747v8.866h11.25v-2.05h-9.197v-6.817H8.242zm31.973 0v6.816h-9.367v2.05h11.418v-8.867h-2.051z"/></symbol></svg>'),d.bind("viewbox.open",function(e,t){Number.isInteger(t)&&i.length?a(i.eq(0<=t&&t<i.length?t:0)):t&&t.tagName?a(q(t)):i.length&&a(i.eq(0))}),d.bind("viewbox.next",function(e){var t;i.length<=1||((t=v()+1)>=i.length&&(t=0),a(i.eq(t)))}),d.bind("viewbox.prev",function(e){var t;i.length<=1||((t=v()-1)<0&&(t=i.length-1),a(i.eq(t)))}),d.bind("viewbox.close",function(e){u&&d.fadeOut(f.closeDuration,function(){u=!1})}),d.bind("viewbox.fullscreen",function(e){var t=m("content").find(".viewbox-image").get(0);t&&(t.requestFullscreen?t.requestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.msRequestFullscreen&&t.msRequestFullscreen())}),i.filter("a").click(function(){return d.trigger("viewbox.open",[this]),!1}),m("body").click(function(e){e.stopPropagation(),f.nextOnContentClick&&d.trigger("viewbox.next")}),f.closeButton&&g("close").click(function(e){e.stopPropagation(),d.trigger("viewbox.close")}),f.navButtons&&1<i.length&&(g("next").click(function(e){e.stopPropagation(),d.trigger("viewbox.next")}),g("prev").click(function(e){e.stopPropagation(),d.trigger("viewbox.prev")})),f.fullscreenButton&&(n=g("full-screen").click(function(e){e.stopPropagation(),d.trigger("viewbox.fullscreen")}),d.on("viewbox.open",function(e,t){h(q(t).attr("href"))?n.show():n.hide()})),f.closeOnSideClick&&d.click(function(){d.trigger("viewbox.close")}),f.useGestures&&"ontouchstart"in document.documentElement&&(e=d,"function"==typeof(c=function(e){switch(e){case"left":d.trigger("viewbox.next");break;case"right":d.trigger("viewbox.prev")}})&&(e.on("touchstart",function(e){var t=e.originalEvent.changedTouches[0];l={x:t.pageX,y:t.pageY},s=(new Date).getTime()}),e.on("touchend",function(e){var t=e.originalEvent.changedTouches[0],n="none",i=(new Date).getTime()-s,o=t.pageX,r=t.pageY,a={x:o-l.x,y:r-l.y};i<=300&&(150<=Math.abs(a.x)&&Math.abs(a.y)<=100?n=a.x<0?"left":"right":150<=Math.abs(a.y)&&Math.abs(a.x)<=100&&(n=a.y<0?"up":"down")),c.call(this,n)}))),d}}(jQuery);