<?php

namespace App\Http\Services\External;

use App\Http\Services\CurlService;
use Illuminate\Support\Facades\Log;

class NazmartProductService
{
    private $nazmart_api_url;
    private $api_key;

    public function __construct()
    {
        $this->nazmart_api_url = config('app.nazmart_api_url', 'http://localhost/nazmart/api');
        $this->api_key = config('app.nazmart_api_key', '');
    }

    /**
     * Create a new product in Nazmart store
     *
     * @param array $productData
     * @return array
     */
    public function createProduct(array $productData): array
    {
        try {
            // Validate required fields
            $requiredFields = ['product_name', 'price', 'category', 'description', 'vendor_id'];
            foreach ($requiredFields as $field) {
                if (empty($productData[$field])) {
                    return [
                        'success' => false,
                        'message' => "Field '{$field}' is required",
                        'data' => null
                    ];
                }
            }

            // Prepare API endpoint
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/products';

            // Prepare headers
            $headers = [
                'Content-Type: application/json',
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            // Prepare data payload
            $payload = [
                'product_name' => $productData['product_name'],
                'price' => $productData['price'],
                'category' => $productData['category'],
                'description' => $productData['description'],
                'image' => $productData['image'] ?? null,
                'vendor_id' => $productData['vendor_id']
            ];

            // Log the request for debugging
            Log::info('Nazmart Product Creation Request', [
                'endpoint' => $endpoint,
                'payload' => $payload
            ]);

            // Make the API call
            $response = CurlService::curlPostRequestWithHeadersJson($endpoint, $headers, $payload);

            // Log the response for debugging
            Log::info('Nazmart Product Creation Response', [
                'response' => $response
            ]);

            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to connect to Nazmart API',
                    'data' => null
                ];
            }

            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from Nazmart API',
                    'data' => null
                ];
            }

            // Check if the API call was successful
            if (isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => $responseData['message'] ?? 'Product created successfully',
                    'data' => $responseData['data'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Failed to create product',
                    'data' => $responseData['errors'] ?? null
                ];
            }

        } catch (\Exception $e) {
            Log::error('Nazmart Product Creation Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the product: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get categories from Nazmart store
     *
     * @param string $storeId
     * @return array
     */
    public function getCategories(string $storeId): array
    {
        try {
            // Prepare API endpoint
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/get-categories';

            // Prepare headers
            $headers = [
                'Content-Type: application/json',
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            // Prepare data payload
            $payload = [
                'store_id' => $storeId
            ];

            // Make the API call
            $response = CurlService::curlPostRequestWithHeadersJson($endpoint, $headers, $payload);

            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to connect to Nazmart API',
                    'data' => []
                ];
            }

            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from Nazmart API',
                    'data' => []
                ];
            }

            if (isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => 'Categories retrieved successfully',
                    'data' => $responseData['data'] ?? []
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Failed to retrieve categories',
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            Log::error('Nazmart Categories Retrieval Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while retrieving categories: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Upload image to Nazmart store
     *
     * @param string $storeId
     * @param mixed $imageFile
     * @return array
     */
    public function uploadImage(string $storeId, $imageFile): array
    {
        try {
            // Prepare API endpoint
            $endpoint = rtrim($this->nazmart_api_url, '/') . '/upload-image';

            // Prepare headers for file upload
            $headers = [
                'Accept: application/json'
            ];

            if (!empty($this->api_key)) {
                $headers[] = 'Authorization: Bearer ' . $this->api_key;
            }

            // Prepare file data
            $postData = [
                'store_id' => $storeId,
                'image' => $imageFile
            ];

            // Make the API call with file upload
            $response = CurlService::curlPostRequestWithFile($endpoint, $headers, $postData);

            if ($response === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to connect to Nazmart API',
                    'data' => null
                ];
            }

            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'message' => 'Invalid response from Nazmart API',
                    'data' => null
                ];
            }

            if (isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => 'Image uploaded successfully',
                    'data' => $responseData['data'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? 'Failed to upload image',
                    'data' => null
                ];
            }

        } catch (\Exception $e) {
            Log::error('Nazmart Image Upload Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while uploading image: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
