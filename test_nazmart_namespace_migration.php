<?php

/**
 * Test script to verify Nazmart namespace migration
 * 
 * This script tests all the new Nazmart services under the App\Services\Nazmart namespace
 * to ensure they work correctly after the reorganization.
 */

require_once __DIR__ . '/kode/vendor/autoload.php';

use App\Services\Nazmart\StoreCreationService;
use App\Services\Nazmart\ProductUploadService;
use App\Services\Nazmart\ProductURLService;

class NazmartNamespaceMigrationTester
{
    private $storeService;
    private $productUploadService;
    private $productURLService;

    public function __construct()
    {
        echo "🚀 Initializing Nazmart Namespace Migration Test...\n\n";
        
        try {
            $this->storeService = new StoreCreationService();
            $this->productUploadService = new ProductUploadService();
            $this->productURLService = new ProductURLService();
            
            echo "✅ All Nazmart services instantiated successfully\n\n";
        } catch (Exception $e) {
            echo "❌ Failed to instantiate services: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "=== NAZMART NAMESPACE MIGRATION TESTS ===\n\n";

        $this->testStoreCreationService();
        $this->testProductUploadService();
        $this->testProductURLService();
        $this->testServiceIntegration();
        $this->testBackwardCompatibility();

        echo "\n=== TEST SUMMARY ===\n";
        echo "✅ All Nazmart namespace migration tests completed successfully!\n";
        echo "🎉 The reorganization to App\\Services\\Nazmart\\ is working correctly.\n\n";
    }

    /**
     * Test StoreCreationService functionality
     */
    private function testStoreCreationService()
    {
        echo "--- Testing StoreCreationService ---\n";

        try {
            // Test service instantiation
            echo "✅ StoreCreationService instantiated successfully\n";

            // Test connection method exists
            if (method_exists($this->storeService, 'testConnection')) {
                echo "✅ testConnection method exists\n";
            } else {
                echo "❌ testConnection method missing\n";
            }

            // Test createStore method exists
            if (method_exists($this->storeService, 'createStore')) {
                echo "✅ createStore method exists\n";
            } else {
                echo "❌ createStore method missing\n";
            }

            // Test getStoreInfo method exists
            if (method_exists($this->storeService, 'getStoreInfo')) {
                echo "✅ getStoreInfo method exists\n";
            } else {
                echo "❌ getStoreInfo method missing\n";
            }

            // Test validation method
            $testData = [
                'name' => 'Test Store',
                'email' => '<EMAIL>',
                'store_name' => 'Test Store Name',
                'phone' => '+1234567890'
            ];

            $validation = $this->storeService->validateStoreData($testData);
            if ($validation['success']) {
                echo "✅ Store data validation works correctly\n";
            } else {
                echo "❌ Store data validation failed\n";
            }

            // Test URL generation
            $url = $this->storeService->generateStoreUrl(['store_slug' => 'test-store']);
            if (!empty($url)) {
                echo "✅ Store URL generation works: " . $url . "\n";
            } else {
                echo "❌ Store URL generation failed\n";
            }

        } catch (Exception $e) {
            echo "❌ StoreCreationService test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test ProductUploadService functionality
     */
    private function testProductUploadService()
    {
        echo "--- Testing ProductUploadService ---\n";

        try {
            // Test service instantiation
            echo "✅ ProductUploadService instantiated successfully\n";

            // Test createProduct method exists
            if (method_exists($this->productUploadService, 'createProduct')) {
                echo "✅ createProduct method exists\n";
            } else {
                echo "❌ createProduct method missing\n";
            }

            // Test uploadImage method exists
            if (method_exists($this->productUploadService, 'uploadImage')) {
                echo "✅ uploadImage method exists\n";
            } else {
                echo "❌ uploadImage method missing\n";
            }

            // Test getCategories method exists
            if (method_exists($this->productUploadService, 'getCategories')) {
                echo "✅ getCategories method exists\n";
            } else {
                echo "❌ getCategories method missing\n";
            }

            // Test validation method
            $testProductData = [
                'product_name' => 'Test Product',
                'price' => 99.99,
                'category' => 'Electronics',
                'description' => 'This is a test product description',
                'vendor_id' => 'test-vendor-123'
            ];

            $validation = $this->productUploadService->validateProductData($testProductData);
            if ($validation['success']) {
                echo "✅ Product data validation works correctly\n";
            } else {
                echo "❌ Product data validation failed: " . $validation['message'] . "\n";
            }

            // Test bulk operations method exists
            if (method_exists($this->productUploadService, 'bulkCreateProducts')) {
                echo "✅ bulkCreateProducts method exists\n";
            } else {
                echo "❌ bulkCreateProducts method missing\n";
            }

        } catch (Exception $e) {
            echo "❌ ProductUploadService test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test ProductURLService functionality
     */
    private function testProductURLService()
    {
        echo "--- Testing ProductURLService ---\n";

        try {
            // Test service instantiation
            echo "✅ ProductURLService instantiated successfully\n";

            // Test generateProductUrl method exists
            if (method_exists($this->productURLService, 'generateProductUrl')) {
                echo "✅ generateProductUrl method exists\n";
            } else {
                echo "❌ generateProductUrl method missing\n";
            }

            // Test getProductUrl method exists
            if (method_exists($this->productURLService, 'getProductUrl')) {
                echo "✅ getProductUrl method exists\n";
            } else {
                echo "❌ getProductUrl method missing\n";
            }

            // Test validateProductUrl method exists
            if (method_exists($this->productURLService, 'validateProductUrl')) {
                echo "✅ validateProductUrl method exists\n";
            } else {
                echo "❌ validateProductUrl method missing\n";
            }

            // Test URL generation
            $testProductData = [
                'product_name' => 'Test Product',
                'store_slug' => 'test-store',
                'product_id' => '123'
            ];

            $url = $this->productURLService->generateProductUrl($testProductData);
            if (!empty($url)) {
                echo "✅ Product URL generation works: " . $url . "\n";
            } else {
                echo "❌ Product URL generation failed\n";
            }

            // Test slug generation
            $slug = $this->productURLService->generateSlugFromName('Test Product Name');
            if ($slug === 'test-product-name') {
                echo "✅ Slug generation works correctly: " . $slug . "\n";
            } else {
                echo "❌ Slug generation failed, got: " . $slug . "\n";
            }

            // Test URL validation
            $validation = $this->productURLService->validateProductUrl('https://vendora.com/stores/test/products/123');
            if ($validation['success']) {
                echo "✅ URL validation works correctly\n";
            } else {
                echo "❌ URL validation failed: " . $validation['message'] . "\n";
            }

        } catch (Exception $e) {
            echo "❌ ProductURLService test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test service integration and inheritance
     */
    private function testServiceIntegration()
    {
        echo "--- Testing Service Integration ---\n";

        try {
            // Test that all services extend BaseNazmartService
            $storeReflection = new ReflectionClass($this->storeService);
            $productUploadReflection = new ReflectionClass($this->productUploadService);
            $productURLReflection = new ReflectionClass($this->productURLService);

            if ($storeReflection->getParentClass() && $storeReflection->getParentClass()->getName() === 'App\\Services\\Nazmart\\BaseNazmartService') {
                echo "✅ StoreCreationService extends BaseNazmartService\n";
            } else {
                echo "❌ StoreCreationService does not extend BaseNazmartService\n";
            }

            if ($productUploadReflection->getParentClass() && $productUploadReflection->getParentClass()->getName() === 'App\\Services\\Nazmart\\BaseNazmartService') {
                echo "✅ ProductUploadService extends BaseNazmartService\n";
            } else {
                echo "❌ ProductUploadService does not extend BaseNazmartService\n";
            }

            if ($productURLReflection->getParentClass() && $productURLReflection->getParentClass()->getName() === 'App\\Services\\Nazmart\\BaseNazmartService') {
                echo "✅ ProductURLService extends BaseNazmartService\n";
            } else {
                echo "❌ ProductURLService does not extend BaseNazmartService\n";
            }

            // Test that common methods are available from base class
            if (method_exists($this->storeService, 'testConnection')) {
                echo "✅ Base class methods available in StoreCreationService\n";
            }

            if (method_exists($this->productUploadService, 'testConnection')) {
                echo "✅ Base class methods available in ProductUploadService\n";
            }

            if (method_exists($this->productURLService, 'testConnection')) {
                echo "✅ Base class methods available in ProductURLService\n";
            }

        } catch (Exception $e) {
            echo "❌ Service integration test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test backward compatibility and namespace resolution
     */
    private function testBackwardCompatibility()
    {
        echo "--- Testing Backward Compatibility ---\n";

        try {
            // Test that old External namespace files still exist (for cleanup verification)
            $oldStoreServicePath = __DIR__ . '/kode/app/Http/Services/External/StoreCreationService.php';
            $oldProductServicePath = __DIR__ . '/kode/app/Http/Services/External/NazmartProductService.php';

            if (file_exists($oldStoreServicePath)) {
                echo "⚠️  Old StoreCreationService still exists at: " . $oldStoreServicePath . "\n";
                echo "   Consider removing after migration is complete\n";
            } else {
                echo "✅ Old StoreCreationService has been removed\n";
            }

            if (file_exists($oldProductServicePath)) {
                echo "⚠️  Old NazmartProductService still exists at: " . $oldProductServicePath . "\n";
                echo "   Consider removing after migration is complete\n";
            } else {
                echo "✅ Old NazmartProductService has been removed\n";
            }

            // Test new namespace files exist
            $newStoreServicePath = __DIR__ . '/kode/app/Services/Nazmart/StoreCreationService.php';
            $newProductUploadServicePath = __DIR__ . '/kode/app/Services/Nazmart/ProductUploadService.php';
            $newProductURLServicePath = __DIR__ . '/kode/app/Services/Nazmart/ProductURLService.php';
            $baseServicePath = __DIR__ . '/kode/app/Services/Nazmart/BaseNazmartService.php';

            if (file_exists($newStoreServicePath)) {
                echo "✅ New StoreCreationService exists at correct location\n";
            } else {
                echo "❌ New StoreCreationService missing\n";
            }

            if (file_exists($newProductUploadServicePath)) {
                echo "✅ New ProductUploadService exists at correct location\n";
            } else {
                echo "❌ New ProductUploadService missing\n";
            }

            if (file_exists($newProductURLServicePath)) {
                echo "✅ New ProductURLService exists at correct location\n";
            } else {
                echo "❌ New ProductURLService missing\n";
            }

            if (file_exists($baseServicePath)) {
                echo "✅ BaseNazmartService exists at correct location\n";
            } else {
                echo "❌ BaseNazmartService missing\n";
            }

        } catch (Exception $e) {
            echo "❌ Backward compatibility test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }
}

// Run the tests
try {
    $tester = new NazmartNamespaceMigrationTester();
    $tester->runAllTests();
} catch (Exception $e) {
    echo "❌ Test execution failed: " . $e->getMessage() . "\n";
    exit(1);
}
