import 'package:flutter/foundation.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/auth/login_usecase.dart';
import '../../core/error/failures.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final LoginUseCase? _loginUseCase;

  AuthProvider({
    LoginUseCase? loginUseCase,
  }) : _loginUseCase = loginUseCase;

  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _errorMessage;

  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  Future<void> login(String email, String password) async {
    _setStatus(AuthStatus.loading);

    if (_loginUseCase == null) {
      // Mock login for development
      await Future.delayed(const Duration(seconds: 2));
      if (email == '<EMAIL>' && password == 'password') {
        _user = User(
          id: '1',
          email: email,
          name: 'Test User',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isVerified: true,
        );
        _setStatus(AuthStatus.authenticated);
      } else {
        _setError('Invalid email or password');
      }
      return;
    }

    final result = await _loginUseCase!(LoginParams(
      email: email,
      password: password,
    ));

    result.fold(
      (failure) {
        _setError(_mapFailureToMessage(failure));
      },
      (user) {
        _user = user;
        _setStatus(AuthStatus.authenticated);
      },
    );
  }

  Future<void> register(String name, String email, String password) async {
    _setStatus(AuthStatus.loading);

    // TODO: Implement register use case
    await Future.delayed(const Duration(seconds: 2));

    // Mock success for now
    _user = User(
      id: '1',
      name: name,
      email: email,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _setStatus(AuthStatus.authenticated);
  }

  Future<void> logout() async {
    _user = null;
    _setStatus(AuthStatus.unauthenticated);
  }

  Future<void> checkAuthStatus() async {
    _setStatus(AuthStatus.loading);

    // TODO: Implement check auth status use case
    await Future.delayed(const Duration(seconds: 1));

    // Mock unauthenticated for now
    _setStatus(AuthStatus.unauthenticated);
  }

  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(AuthStatus.error);
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case AuthFailure:
        return 'Invalid email or password';
      case NetworkFailure:
        return 'Please check your internet connection';
      case ServerFailure:
        return 'Server error. Please try again later';
      default:
        return 'An unexpected error occurred';
    }
  }
}
