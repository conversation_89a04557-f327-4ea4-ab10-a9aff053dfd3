@php use Illuminate\Support\Arr; @endphp
@extends('layouts.master')
@section('content')

@push('style-include')
    <link nonce="{{ csp_nonce() }}"  href="{{asset('assets/frontend/css/post.css')}}" rel="stylesheet" type="text/css">
    <link nonce="{{ csp_nonce() }}" href="{{asset('assets/global/css/datepicker/daterangepicker.css')}}" rel="stylesheet" type="text/css" />
    <style>

    /* Enhanced Account Selection Styles */
    .account-selection-wrapper {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
    }

    .account-item-enhanced {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px;
        transition: all 0.2s ease;
        background-color: #fff;
    }

    .account-item-enhanced:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,123,255,0.1);
    }

    .account-checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .account-checkbox {
        margin: 0;
        transform: scale(1.2);
    }

    .account-label-enhanced {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        cursor: pointer;
        margin: 0;
    }

    .account-avatar-enhanced {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e0e0e0;
    }

    .account-info-enhanced {
        display: flex;
        flex-direction: column;
    }

    .account-name-enhanced {
        font-weight: 500;
        color: #333;
        font-size: 14px;
    }

    .platform-name-enhanced {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
    }

    .store-link-wrapper-enhanced {
        margin-left: auto;
    }

    .store-link-btn-enhanced {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 6px;
        padding: 8px 10px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: white;
        font-size: 14px;
        box-shadow: 0 2px 4px rgba(0,123,255,0.2);
    }

    .store-link-btn-enhanced:hover:not(.disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,123,255,0.3);
    }

    .store-link-btn-enhanced.disabled {
        background: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .store-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        display: none;
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }

    .store-modal.show {
        display: flex;
    }

    .store-modal-content {
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }

    .store-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e0e0e0;
    }

    .store-modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .store-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .store-modal-close:hover {
        color: #333;
    }

    .store-info-section {
        background-color: #f8f9fa;
        padding: 16px;
        border-radius: 6px;
        border-left: 4px solid #007bff;
        margin-bottom: 20px;
    }

    .store-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .store-btn {
        padding: 10px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .store-btn-primary {
        background-color: #007bff;
        color: white;
    }

    .store-btn-primary:hover {
        background-color: #0056b3;
        color: white;
        text-decoration: none;
    }

    .store-btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .store-btn-secondary:hover {
        background-color: #545b62;
        color: white;
    }

    .no-store-section {
        text-align: center;
        padding: 40px 20px;
    }

    .no-store-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 16px;
    }

    @media (max-width: 768px) {
        .account-checkbox-wrapper {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .store-link-wrapper-enhanced {
            margin-left: 0;
            align-self: flex-end;
        }

        .store-actions {
            flex-direction: column;
        }

        .store-btn {
            width: 100%;
            justify-content: center;
        }

        .store-modal-content {
            margin: 20px;
            width: calc(100% - 40px);
        }
    }

    /* AI Assistant Dropdown Enhancements */
    .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        padding: 10px 16px;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle:focus {
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        outline: none;
    }

    .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle::after {
        margin-left: 8px;
        transition: transform 0.3s ease;
    }

    .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle[aria-expanded="true"]::after {
        transform: rotate(180deg);
    }


    /* Dropdown Menu Styling */
    .compose-body-bottom .dropdown .dropdown-menu {

        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        background: white;
        padding: 8px !important;
        margin-top: 8px;
        animation: dropdownFadeIn 0.2s ease-out;
        min-width : 200px !important;
    }

    @keyframes dropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Dropdown Items */
    .compose-body-bottom .dropdown .dropdown-menu li {
        margin-bottom: 4px;
    }

    .compose-body-bottom .dropdown .dropdown-menu li:last-child {
        margin-bottom: 0;
    }

    .compose-body-bottom .dropdown .dropdown-menu .ai-modal,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-modal,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-modal,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-gallery-modal,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-gallery-modal {
        border-radius: 8px;
        transition: all 0.2s ease;
        padding: 12px 16px !important;
        margin: 0;
        background: transparent;
        border: 1px solid transparent;
    }

    .compose-body-bottom .dropdown .dropdown-menu .ai-modal:hover,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-modal:hover,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-modal:hover,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-gallery-modal:hover,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-gallery-modal:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
        border-color: #e0e7ff;
        transform: translateX(4px);
    }

    /* Icons Styling */
    .compose-body-bottom .dropdown .dropdown-menu .bi {
        font-size: 18px;
        color: #667eea;
        width: 20px;
        text-align: center;
    }

    .compose-body-bottom .dropdown .dropdown-menu .bi-robot {
        color: #667eea;
    }

    .compose-body-bottom .dropdown .dropdown-menu .bi-images {
        color: #f59e0b;
    }

    .compose-body-bottom .dropdown .dropdown-menu .bi-film {
        color: #ef4444;
    }

    /* Text Styling */
    .compose-body-bottom .dropdown .dropdown-menu p {
        margin: 0;
        font-weight: 500;
        color: #374151;
        font-size: 14px;
        line-height: 1.4;
    }

    .compose-body-bottom .dropdown .dropdown-menu .ai-modal:hover p,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-modal:hover p,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-modal:hover p,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-gallery-modal:hover p,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-gallery-modal:hover p {
        color: #1f2937;
    }

    /* Active/Focus States */
    .compose-body-bottom .dropdown .dropdown-menu .ai-modal:active,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-modal:active,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-modal:active,
    .compose-body-bottom .dropdown .dropdown-menu .ai-image-gallery-modal:active,
    .compose-body-bottom .dropdown .dropdown-menu .ai-video-gallery-modal:active {
        transform: translateX(2px);
        background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .compose-body-bottom .dropdown .dropdown-menu {
            min-width: 220px !important;
        }

        .compose-body-bottom .dropdown .action-item.image-dropdwon.dropdown-toggle {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    /* Main Tab Styles */
    .main-tabs {
        margin-bottom: 30px;
    }

    .main-tabs .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 0;
    }

    .main-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        background: none;
        color: #6c757d;
        font-weight: 600;
        padding: 15px 25px;
        transition: all 0.3s ease;
    }

    .main-tabs .nav-link:hover {
        color: #007bff;
        border-bottom-color: #007bff;
        background: none;
    }

    .main-tabs .nav-link.active {
        color: #007bff;
        border-bottom-color: #007bff;
        background: none;
    }

    .main-tabs .nav-link i {
        margin-right: 8px;
    }

    .tab-content {
        padding-top: 20px;
    }

    /* Product Form Styles */
    .product-form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .image-upload-area:hover {
        border-color: #007bff;
        background: #e3f2fd;
    }

    .image-upload-area.dragover {
        border-color: #007bff;
        background: #e3f2fd;
    }

    .image-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        margin-top: 10px;
    }

    .post-after-upload {
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
    }

    .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* Enhanced AI Styles */
    .platform-content-indicator {
        margin-top: 8px;
    }

    .platform-content-indicator .badge {
        margin-right: 5px;
        font-size: 0.75rem;
    }

    .platform-warning {
        margin-top: 15px;
    }

    .platform-warning .alert {
        padding: 8px 12px;
        margin-bottom: 8px;
        font-size: 0.875rem;
    }

    .platform-content-preview {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }

    .platform-preview-item {
        padding: 10px;
        background: white;
        border-radius: 6px;
        border-left: 4px solid #007bff;
    }

    .preview-content {
        font-style: italic;
        color: #6c757d;
        margin: 5px 0;
    }

    .enhanced-ai-indicator {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    #enhancedAiOptions {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }

    #enhancedAiOptions h4 {
        color: #495057;
        margin-bottom: 15px;
    }

    #enhancedAiOptions .checkbox-wrapper {
        margin-bottom: 10px;
    }

    #enhancedAiOptions .checkbox-wrapper label {
        font-weight: 500;
        color: #495057;
    }

    #enhancedAiOptions small {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    </style>
@endpush

@section('content')
@php
    $user = auth_user('web')->load(['runningSubscription','runningSubscription.package']);
    $schedule = false;

    $notes = trans('default.platform_notes') ;

    if($user->runningSubscription){
        $package = $user->runningSubscription->package;
        if($package && @$package->social_access->schedule_post == App\Enums\StatusEnum::true->status()) $schedule = true;
    }
@endphp

<div class="compose-wrapper">

    <!-- Main Tabs -->
    <div class="main-tabs">
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="post-tab" data-bs-toggle="tab" data-bs-target="#post-content" type="button" role="tab" aria-controls="post-content" aria-selected="true">
                    <i class="fas fa-share-alt"></i> {{translate('Create Post')}}
                </button>
            </li>
            @if(auth_user()->nazmart_store_id)
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="product-tab" data-bs-toggle="tab" data-bs-target="#product-content" type="button" role="tab" aria-controls="product-content" aria-selected="false">
                    <i class="fas fa-box"></i> {{translate('Create Product')}}
                </button>
            </li>
            @endif
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="mainTabContent">

        <!-- Post Creation Tab -->
        <div class="tab-pane fade show active" id="post-content" role="tabpanel" aria-labelledby="post-tab">
            <form action="{{route('user.social.post.store')}}" method="post" class="compose-form" enctype="multipart/form-data">
        @csrf
        <div class="row g-4">
            <div class="col-xxl-8 col-lg-7">
                <div class="i-card-md">
                    <div class="card-body">
                        <div>
                            <div class="mb-4">
                                <div class="d-flex justify-content-between">
                                    <h4 class="card-title mb-3">{{translate('Where to post')}}</h4>
                                </div>

                                @if($platforms->count() > 0)
                                    <div class="row gy-3">
                                        <div class="col-xl-6 col-lg-12 col-md-6">
                                            <ul class="nav nav-tabs post-select-tab" id="postTypeTab" role="tablist">
                                                @foreach($platforms as  $platform)
                                                    <li class="nav-item" role="presentation">
                                                        <button  class="nav-link  {{ $loop->index == 0 ? 'active' : '' }} " id="{{$platform->slug}}-tab" data-bs-toggle="tab" data-bs-target="#{{$platform->slug}}-tab-pane" type="button" role="tab" aria-controls="{{$platform->slug}}-tab-pane" aria-selected="true">
                                                            <img src="{{imageURL(@$platform->file,'platform',true)}}" alt="{{@$platform->name .translate( 'Feature image')}}">
                                                        </button>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>

                                        <div class="col-xl-6 col-lg-12 col-md-6 d-flex
                                                    justify-content-xl-end
                                                    justify-content-lg-start
                                                    justify-content-md-end
                                                    justify-content-start">
                                            <div class="tab-content" id="postTypeTabContent">
                                                @foreach ($platforms as  $platform)
                                                    @php

                                                        $postTypes = App\Enums\PostType::toArray();
                                                        if($platform->slug == 'facebook') $postTypes =  Arr::except( $postTypes,[App\Enums\PostType::STORY->name]);
                                                        if($platform->slug == 'twitter') $postTypes  =  Arr::except( $postTypes,[App\Enums\PostType::REELS->name,App\Enums\PostType::STORY->name]);
                                                        if($platform->slug == 'linkedin') $postTypes =  Arr::except( $postTypes,[App\Enums\PostType::REELS->name,App\Enums\PostType::STORY->name]);
                                                        if($platform->slug == 'tiktok')   $postTypes   =  Arr::except( $postTypes,[App\Enums\PostType::REELS->name,App\Enums\PostType::STORY->name]);
                                                        if($platform->slug == 'whatsapp') $postTypes =  Arr::only( $postTypes,[App\Enums\PostType::WHATSAPP_STATUS->name]);

                                                    @endphp

                                                    <div class="tab-pane fade  {{ $loop->index == 0 ? 'show active' : '' }}" id="{{$platform->slug}}-tab-pane" role="tabpanel" aria-labelledby="{{$platform->slug}}-tab" tabindex="0">
                                                        <div class="d-flex gap-2 align-items-center">
                                                            @foreach ($postTypes as  $type => $value)
                                                                <div class="radio--button">
                                                                    <input {{ $loop->index == 0 ? 'checked' : ''}}  type="radio" id="post_type_{{$platform->slug}}-{{$loop->index}}"  name="post_type[{{$platform->slug}}]" value="{{$value}}" />
                                                                    <label for="post_type_{{$platform->slug}}-{{$loop->index}}"> {{$type}}</label>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <div class="mt-3">
                                    <div class="d-flex justify-content-between">
                                        <h4 class="card-title mb-3">{{translate('Choose Profile')}}</h4>
                                    </div>

                                    <!-- Enhanced Account Selection with Store Links -->
                                    <div class="account-selection-wrapper">
                                        @foreach (@$accounts as $account)
                                            @php
                                                $imgUrl = isValidImageUrl(@$account->account_information->avatar)
                                                    ? @$account->account_information->avatar
                                                    : route('default.image', '200x200');
                                                $platformSlug = strtolower(@$account->platform->slug);
                                                $showStoreLink = in_array($platformSlug, ['instagram', 'whatsapp', 'facebook']);
                                            @endphp

                                            <div class="account-item-enhanced">
                                                <div class="account-checkbox-wrapper">
                                                    <input
                                                        type="checkbox"
                                                        name="account_id[]"
                                                        value="{{ $account->id }}"
                                                        id="account_{{ $account->id }}"
                                                        class="account-checkbox"
                                                        @if(old('account_id') && is_array(old('account_id')) && in_array($account->id, old('account_id'))) checked @endif
                                                    />
                                                    <label for="account_{{ $account->id }}" class="account-label-enhanced">
                                                        <img src="{{ $imgUrl }}" alt="{{ $account->name }}" class="account-avatar-enhanced" />
                                                        <div class="account-info-enhanced">
                                                            <span class="account-name-enhanced">{{ $account->name }}</span>
                                                            <span class="platform-name-enhanced">{{ @$account->platform->name }}</span>
                                                        </div>
                                                    </label>

                                                    @if($showStoreLink)
                                                        <div class="store-link-wrapper-enhanced">
                                                            <button
                                                                type="button"
                                                                class="store-link-btn-enhanced {{ !@$user_store_data['nazmart_store_slug'] ? 'disabled' : '' }}"
                                                                onclick="openStoreModal('{{ $platformSlug }}')"
                                                                title="{{ translate('Store Link') }}"
                                                                {{ !@$user_store_data['nazmart_store_slug'] ? 'disabled' : '' }}
                                                            >
                                                                <i class="bi bi-shop"></i>
                                                            </button>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Store Link Modal -->
                            <div id="storeModal" class="store-modal">
                                <div class="store-modal-content">
                                    <div class="store-modal-header">
                                        <h5 class="store-modal-title">{{ translate('Store Preview') }}</h5>
                                        <button type="button" class="store-modal-close" onclick="closeStoreModal()">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                    <div class="store-modal-body">
                                        @if(!@$user_store_data['nazmart_store_slug'])
                                            <div class="no-store-section">
                                                <div class="no-store-icon">
                                                    <i class="bi bi-shop"></i>
                                                </div>
                                                <h5>{{ translate('No Store Connected') }}</h5>
                                                <p class="text-muted mb-3">{{ translate('Connect your Nazmart store to share products on social media.') }}</p>
                                                <a href="{{ route('admin.store.create') }}" class="store-btn store-btn-primary">
                                                    <i class="bi bi-plus-circle"></i>
                                                    {{ translate('Create Store') }}
                                                </a>
                                            </div>
                                        @else
                                            <div class="store-info-section">
                                                <h6 class="mb-2">{{ translate('Your Store') }}</h6>
                                                <p class="mb-1"><strong>{{ translate('Store URL') }}:</strong></p>
                                                <p class="mb-0">
                                                    <a href="https://vendora.com/stores/{{ $user_store_data['nazmart_store_slug'] }}"
                                                       target="_blank"
                                                       class="text-primary">
                                                        https://vendora.com/stores/{{ $user_store_data['nazmart_store_slug'] }}
                                                    </a>
                                                </p>
                                            </div>
                                            <div class="store-actions">
                                                <a href="https://vendora.com/stores/{{ $user_store_data['nazmart_store_slug'] }}"
                                                   target="_blank"
                                                   class="store-btn store-btn-primary">
                                                    <i class="bi bi-box-arrow-up-right"></i>
                                                    {{ translate('Visit Store') }}
                                                </a>
                                                <button type="button"
                                                        onclick="copyStoreUrl('https://vendora.com/stores/{{ $user_store_data['nazmart_store_slug'] }}')"
                                                        class="store-btn store-btn-secondary">
                                                    <i class="bi bi-clipboard"></i>
                                                    {{ translate('Copy URL') }}
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h4 class="card-title mb-3">
                                    {{translate('Create your post')}}
                                </h4>

                                <div class="caption-wrapper">
                                    @if(session('product_for_posting.use_enhanced_ai'))
                                        <div class="alert alert-info mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-robot fs-4 me-2"></i>
                                                <div>
                                                    <strong>{{translate('Product Detected!')}}</strong><br>
                                                    <small>{{translate('Use AI-Enhanced Content Generation for optimized social media posts with your product URL.')}}</small>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="form-inner mb-0">
                                        <div class="compose-body">
                                            <textarea name="text" cols="30" rows="4"
                                                placeholder="{{translate('Start Writing')}}" class="compose-input post-intput"
                                                id="inputText" contenteditable="true">{{old('text')}}</textarea>

                                            <div class="compose-body-bottom">
                                                <div class="caption-action d-flex justify-content-start">


                                                     <div class="dropdown">
                                                        <button class="action-item image-dropdwon dropdown-toggle"
                                                            type="button" data-bs-toggle="dropdown"
                                                            aria-expanded="false">
                                                            {{translate("AI Assistant")}}
                                                        </button>

                                                        <ul class="dropdown-menu p-2">

                                                            <li>

                                                                <div
                                                                    class="ai-modal d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                                    <i class="bi bi-card-text"></i>
                                                                    <p>
                                                                        {{translate("Generate Text")}}
                                                                    </p>
                                                                </div>
                                                            </li>
                                                            <li>

                                                                <div
                                                                    class="ai-image-modal d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                                    <i class="bi bi-image-alt"></i>
                                                                    <p>
                                                                        {{translate("Generate Image")}}
                                                                    </p>
                                                                </div>
                                                            </li>

                                                            <li>

                                                                <div
                                                                    class="ai-video-modal d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                                    <i class="bi bi-camera-reels"></i>
                                                                    <p>
                                                                        {{translate("Generate Video")}}
                                                                    </p>
                                                                </div>
                                                            </li>



                                                            <li>

                                                                <div
                                                                    class="ai-image-gallery-modal d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                                    <i class="bi bi-images"></i>
                                                                    <p>
                                                                        {{translate("Import Image")}}
                                                                    </p>
                                                                </div>
                                                            </li>

                                                            <li>

                                                                <div
                                                                    class="ai-video-gallery-modal d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                                    <i class="bi bi-film"></i>
                                                                    <p>
                                                                        {{translate("Import Video")}}
                                                                    </p>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>

                                                    <div class="upload-filed">
                                                        <input id="media-file" multiple type="file" name="files[]">
                                                        <label for="media-file" class="d-flex align-items-center gap-2 cursor-pointer px-2 py-1">
                                                            <span class="d-flex flex-row gap-2">
                                                                <span class="upload-drop-file">
                                                                    <i class="bi bi-image fs-20"></i>
                                                                </span>
                                                                <span>
                                                                    {{translate('Photo/Video')}}
                                                                </span>
                                                            </span>
                                                        </label>
                                                    </div>

                                                    <div>
                                                        <select class="form-select predefined-select" aria-label="Default select example" id="predefined">
                                                            <option value="">{{translate("Predefined Content")}}</option>
                                                            @foreach($contents as  $content)
                                                                <option value="{{$content->content}}">
                                                                    {{$content->name}}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>

                                                    @if($schedule)
                                                        <div class="schedule-btn">
                                                            <div class="px-3 custom-date-label" id="schedule_date_picker" data-bs-toggle="tooltip"
                                                                data-bs-title="{{translate('Schedule Post')}}">
                                                                <i class="bi bi-clock"></i>
                                                            </div>
                                                            <p class="show-date"></p>
                                                        </div>
                                                       <input type="datetime-local"  hidden name="schedule_date" id="schedule_date_input" >

                                                    @endif
                                                </div>
                                                <ul class="file-list mt-3"></ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="mb-4">
                                <h4 class="card-title mb-3">{{translate('Links')}}</h4>
                                <div class="input-group mb-0">
                                    <input type="text" placeholder="{{translate('Enter link')}}" name="link" id="link" value="{{old('link')}}" class="form-control" />
                                </div>
                            </div>

                            <!-- Enhanced AI Options -->
                            <div class="mb-4" id="enhancedAiOptions" style="display: none;">
                                <h4 class="card-title mb-3">
                                    <i class="bi bi-robot"></i> {{translate('AI Enhancement Options')}}
                                </h4>

                                <div class="checkbox-wrapper mb-3">
                                    <input type="checkbox" id="post_to_website_first" name="post_to_website_first" value="1">
                                    <label for="post_to_website_first" class="form-label">
                                        {{translate('Post to website first')}}
                                    </label>
                                    <small class="text-muted d-block mt-1">
                                        {{translate('Create product on your store first, then generate AI-enhanced content with product URL')}}
                                    </small>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="checkbox-wrapper mb-2">
                                            <input type="checkbox" id="include_hashtags" name="include_hashtags" value="1" checked>
                                            <label for="include_hashtags" class="form-label">{{translate('Include Hashtags')}}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="checkbox-wrapper mb-2">
                                            <input type="checkbox" id="include_emojis" name="include_emojis" value="1" checked>
                                            <label for="include_emojis" class="form-label">{{translate('Include Emojis')}}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="checkbox-wrapper mb-2">
                                            <input type="checkbox" id="include_cta" name="include_cta" value="1" checked>
                                            <label for="include_cta" class="form-label">{{translate('Include Call-to-Action')}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button  type="submit"
                                class="i-btn btn--primary btn--lg capsuled postSubmitButton"
                                id="postSubmitButton">
                                {{translate("Post")}}
                                <i class="bi bi-send"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xxl-4 col-lg-5">
                <div class="i-card-md social-preview-user">
                    <div class="card-header">
                        <h4 class="card-title">
                            {{translate("Suggestion/Preview")}}
                        </h4>
                    </div>
                    <div class="card-body">

                        <div class="d-flex flex-column gap-4">
                            @foreach ($platforms as  $platform)


                                  @php
                                      $note = Arr::get($notes , $platform->slug);
                                  @endphp

                                    <div class="flip-wrapper">

                                        <div class=" platform-note post-before-social-card d-flex justify-content-start gap-3 align-items-start">
                                            <div class="icon facebook">
                                                <i class="bi bi-{{$platform->slug}}"></i>
                                            </div>
                                            <div class="content">
                                                <h5 class="mb-3">
                                                    {{
                                                        k2t($platform->slug)
                                                    }}
                                                </h5>
                                                <p>{{$note}}</p>
                                            </div>
                                        </div>

                                        <div class="social-preview-body fade-in  d-none {{$platform->slug }}">

                                            <div class="post-logo">
                                                <img data-bs-toggle="tooltip" data-bs-title="{{$platform->name . translate(' Preview')}}" src="{{e(imageURL(@$platform->file,'platform',true))}}" alt="{{@$platform->name .translate( 'Feature image')}}">
                                            </div>

                                            <div class="social-auth">
                                                <div class="profile-img">
                                                    <img src="{{e(get_default_img())}}"
                                                        alt="{{translate('Fallback default image')}}" />
                                                </div>

                                                <div class="profile-meta">
                                                    <h6 class="user-name">

                                                            {{translate('Username')}}

                                                    </h6>
                                                    @if($platform->slug == 'facebook')
                                                        <div class="d-flex align-items-center gap-2">
                                                            <p>
                                                            {{Carbon\Carbon::now()->format('F j')}}
                                                            </p>
                                                            <i class="bi bi-globe-americas fs-12"></i>
                                                        </div>
                                                    @else
                                                        <p>
                                                           {{Carbon\Carbon::now()->format('F j')}}
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="social-caption">
                                                <div class="caption-text">
                                                </div>
                                                <div class="caption-imgs position-relative">
                                                    <div class="caption-img caption-placeholder">
                                                        <img class="w-100 h-100" src="{{get_default_img()}}" alt="Default Image">
                                                    </div>
                                                </div>
                                                <div class="caption-link"></div>


                                                <div class="caption-action">
                                                    @if($platform->slug == 'facebook')
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-hand-thumbs-up"></i>
                                                            <span>{{translate('Like')}}</span>
                                                        </div>

                                                        <div class="caption-action-item">
                                                            <i class="bi bi-chat-right"></i>
                                                            <span>{{translate('Comment')}}</span>
                                                        </div>

                                                        <div class="caption-action-item">
                                                            <i class="bi bi-share"></i>
                                                            <span>{{translate('Share')}}</span>
                                                        </div>
                                                    @elseif($platform->slug == 'instagram')
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-heart"></i>
                                                        </div>
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-chat-right"></i>
                                                        </div>
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-send"></i>
                                                        </div>
                                                    @elseif($platform->slug == 'twitter')
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-chat-right"></i>
                                                        </div>
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-repeat"></i>
                                                        </div>
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-heart"></i>
                                                        </div>
                                                    @elseif($platform->slug == 'linkedin')
                                                        <div class="caption-action-item">
                                                            <i class="bi bi-hand-thumbs-up"></i>
                                                            <span>{{translate('Like')}}</span>
                                                        </div>

                                                        <div class="caption-action-item">
                                                            <i class="bi bi-chat-right"></i>
                                                            <span>{{translate('Comment')}}</span>
                                                        </div>

                                                        <div class="caption-action-item">
                                                            <i class="bi bi-repeat"></i>
                                                            <span>{{translate('Repost')}}</span>
                                                        </div>

                                                        <div class="caption-action-item">
                                                            <i class="bi bi-send"></i>
                                                            <span>{{translate('Send')}}</span>
                                                        </div>
                                                    @endif
                                                </div>

                                            </div>
                                        </div>

                                    </div>



                            @endforeach
                        </div>

                        @if($platforms->count() == 0)
                           @include('admin.partials.not_found')
                        @endif
                    </div>
                </div>
            </div>
        </div>
            </form>
        </div>

        <!-- Product Creation Tab -->
        @if(auth_user()->nazmart_store_id)
        <div class="tab-pane fade" id="product-content" role="tabpanel" aria-labelledby="product-tab">
            <div class="row g-4">
                <div class="col-12">
                    <div class="i-card-md">
                        <div class="card-header">
                            <h4 class="card-title">{{translate('Create Product')}}</h4>
                        </div>
                        <div class="card-body">
                            <div class="product-form-container">

                                <!-- Store Information -->
                                <div class="alert alert-info">
                                    <strong>{{translate('Store')}}:</strong>
                                    <a href="{{auth_user()->nazmart_store_url}}" target="_blank">{{auth_user()->nazmart_store_slug}}</a>
                                </div>

                                <!-- Product Creation Form -->
                                <form id="productForm" enctype="multipart/form-data">
                                    @csrf

                                    <!-- Product Name -->
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="product_name">{{translate('Product Name')}} *</label>
                                        <input type="text" id="product_name" name="product_name" class="form-control"
                                               placeholder="{{translate('Enter product name')}}" required>
                                    </div>

                                    <!-- Price -->
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="price">{{translate('Price')}} *</label>
                                        <input type="number" id="price" name="price" class="form-control"
                                               placeholder="{{translate('Enter price')}}" step="0.01" min="0" required>
                                    </div>

                                    <!-- Category -->
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="category">{{translate('Category')}} *</label>
                                        <select id="category" name="category" class="form-control" required>
                                            <option value="">{{translate('Select Category')}}</option>
                                        </select>
                                    </div>

                                    <!-- Description -->
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="description">{{translate('Description')}} *</label>
                                        <textarea id="description" name="description" class="form-control" rows="4"
                                                  placeholder="{{translate('Enter product description')}}" required></textarea>
                                    </div>

                                    <!-- Image Upload -->
                                    <div class="form-group mb-3">
                                        <label class="form-label">{{translate('Product Image')}}</label>
                                        <div class="image-upload-area" id="imageUploadArea">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">{{translate('Click to upload or drag and drop')}}</p>
                                            <p class="text-muted small">{{translate('Supported formats: JPG, PNG, GIF (Max: 2MB)')}}</p>
                                            <input type="file" id="image" name="image" accept="image/*" style="display: none;">
                                        </div>
                                        <div id="imagePreview" style="display: none;">
                                            <img id="previewImg" class="image-preview" alt="Preview">
                                            <button type="button" class="btn btn-sm btn-danger mt-2" id="removeImage">
                                                {{translate('Remove Image')}}
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Post After Upload Option -->
                                    <div class="post-after-upload mb-3">
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="post_after_upload" name="post_after_upload" value="1" checked>
                                            <label for="post_after_upload" class="form-label">
                                                {{translate('Post this product to social media after creation')}}
                                            </label>
                                        </div>
                                        <p class="text-muted small mt-2">
                                            {{translate('If checked, you will be redirected to the post scheduler with this product pre-filled.')}}
                                        </p>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary" id="submitBtn">
                                            {{translate('Create Product')}}
                                        </button>
                                    </div>

                                </form>

                                <!-- Loading Overlay -->
                                <div id="loadingOverlay" style="display: none;">
                                    <div class="text-center">
                                        <div class="spinner"></div>
                                        <p class="mt-2">{{translate('Creating product...')}}</p>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

    </div>
</div>


@endsection

@section('modal')


<div class="modal fade" id="aiModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="aiModal">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title ai-modal-title">
                    {{translate('Generate Content')}}
                </h5>

                <button class="icon-btn icon-btn-sm danger" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>

            <div class="modal-body modal-body-section">
                @include('partials.prompt_content',['content_route' => route("user.ai.content.store"),'modal' => true])
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="aiImageModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="aiImageModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title ai-modal-title">
                  {{translate('Generate Image Content')}}
                </h5>
                <button class="icon-btn icon-btn-sm danger" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body modal-body-section">
                @include('partials.prompt_image_content',['content_route' => route("user.ai.content.image.store"),'modal' => true])
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="aiVideoModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="aiImageModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title ai-modal-title">
                  {{translate('Generate Video Content')}}
                </h5>
                <button class="icon-btn icon-btn-sm danger" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body modal-body-section">
                @include('partials.prompt_video_content',['content_route' => route("user.ai.content.video.store"),'modal' => true])
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="aiImageGalleryModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="aiVideoGalleryModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title ai-modal-title">
                  {{translate('Image gallery')}}
                </h5>
                <button class="icon-btn icon-btn-sm danger" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body modal-body-section">
                @include('partials.image_gallery')
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="aiVideoGalleryModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="aiVideoGalleryModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title ai-modal-title">
                  {{translate('Video gallery')}}
                </h5>
                <button class="icon-btn icon-btn-sm danger" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body modal-body-section">
                @include('partials.video_gallery')
            </div>
        </div>
    </div>
</div>




@endsection

@push('script-include')
     @include('partials.ai_content_script');
    <script nonce="{{ csp_nonce() }}" src="{{asset('assets/global/js/post.js')}}"></script>
    <script nonce="{{ csp_nonce() }}" src="{{asset('assets/global/js/datepicker/moment.min.js')}}"></script>
    <script nonce="{{ csp_nonce() }}" src="{{asset('assets/global/js/datepicker/daterangepicker.min.js')}}"></script>
@endpush


@push('script-push')
<script nonce="{{ csp_nonce() }}">
(function($) {
    "use strict";

    $(document).on('change', '#predefined', function(e) {
        e.preventDefault()
        var value = $(this).val();
        var cleanContent = DOMPurify.sanitize(value);
        $("#inputText").val(cleanContent)
        $(".caption-text").html(cleanContent)
        $('.platform-note').addClass('d-none');
        $('.social-preview-body').removeClass('d-none');

    })

    $(".user").select2({  placeholder:"{{translate('Select user')}}"})

    $(document).on('click', '.ai-modal', function(e) {
        e.preventDefault()
        var modal = $('#aiModal');
        modal.find('.ai-content-form')[0].reset();
        modal.find('.ai-content-div').addClass("d-none")
        modal.find('#ai-form').fadeIn()
        modal.find('.ai-modal-title').html("{{translate('Generate Content')}}")
        modal.modal('show');

    });

    function toggleContentPreview(inputText , modal){

        if (inputText.length > 0) {
            modal.find('#contentPreviewSection').removeClass('d-none');
            modal.find('#postPreview').val(inputText);
        } else {
            modal.find('#contentPreviewSection').addClass('d-none');
            modal.find('#postPreview').val('');
        }

    }

    $(document).on('click', '.ai-image-modal', function(e) {
        e.preventDefault()
        var modal       = $('#aiImageModal');
        var inputText   = $('#inputText').val();
        modal.find('.ai-content-image-form')[0].reset();
        toggleContentPreview(inputText , modal)
        modal.find('.ai-content-div').addClass("d-none")
        modal.find('#ai-image-form').fadeIn()
        modal.find('.ai-modal-title').html("{{translate('Generate Image Content')}}")
        modal.modal('show');
    });

    $(document).on('click', '.ai-video-modal', function(e) {
        e.preventDefault()
        var modal       = $('#aiVideoModal');
        var inputText   = $('#inputText').val();
        modal.find('.ai-content-video-form')[0].reset();
        toggleContentPreview(inputText , modal)
        modal.find('.ai-content-div').addClass("d-none")
        modal.find('#ai-video-form').fadeIn()
        modal.find('.ai-modal-title').html("{{translate('Generate Video Content')}}")
        modal.modal('show');
    });

    $(document).on('click', '.ai-image-gallery-modal', function(e) {
        e.preventDefault()
        var modal = $('#aiImageGalleryModal');
        modal.find('.ai-modal-title').html("{{translate('Image gallery')}}")
        modal.modal('show');
    });

    $(document).on('click', '.ai-video-gallery-modal', function(e) {
        e.preventDefault()
        var modal = $('#aiVideoGalleryModal');
        modal.find('.ai-modal-title').html("{{translate('Video gallery')}}")
        modal.modal('show');
    });


    $(".select2").select2({
        placeholder:"{{translate('Select Category')}}",
        dropdownParent: $("#aiModal"),
    })
    $(".language").select2({
        placeholder:"{{translate('Select Language')}}",
        dropdownParent: $("#aiModal"),
    })

    $(".selectTemplate").select2({
        placeholder:"{{translate('Select Template')}}",
        dropdownParent: $("#aiModal"),
    })
    $(".sub_category_id").select2({
        placeholder:"{{translate('Select Sub Category')}}",
        dropdownParent: $("#aiModal"),
    })

    $(document).on('click', '.copy-content', function(e) {
        e.preventDefault()
        var textarea = document.getElementById('content');
        textarea.select();
        document.execCommand('copy');
        window.getSelection().removeAllRanges();
        toastr("{{translate('Text copied to clipboard!')}}", 'success');
    });


    $(document).on('click', '.insert-text', function(e) {

        e.preventDefault()
        var content = $('textarea#content').val();
        var cleanContent = DOMPurify.sanitize(content);
        $('.post-intput').val(cleanContent)
        var modal = $('#aiModal');
        modal.modal('hide');

        $(".caption-text").html(cleanContent);

        $('.platform-note').addClass('d-none');
        $('.social-preview-body').removeClass('d-none');



    });

    $(document).on('click', '.download-text', function(e) {
        e.preventDefault()
        var content = document.getElementById('content').value;
        var cleanContent = DOMPurify.sanitize(content);

        var blob = new Blob([cleanContent], {
            type: 'text/html'
        });
        var link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = 'downloaded_content.html';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });

    $(document).on('click', '.insert-image', function(e) {
        e.preventDefault();

        if ($(this).hasClass('disabled')) return;
        $(this).addClass('disabled');

        const modalName = $(this).data('modal');
        const $modal = $(`#${modalName}`);

        const imageUrls = $modal.find('.image-check:checked').map(function() {
            return $(this).val();
        }).get();

        if (!imageUrls || imageUrls.length === 0) {
            console.log('Error: No image URLs found');
            $(this).removeClass('disabled');
            return;
        }

        const $platformNote = $('.platform-note');
        const $socialPreviewBody = $('.social-preview-body');
        const $fileInput = $('#media-file');



        requestAnimationFrame(() => {
            $platformNote.addClass('d-none');
            $socialPreviewBody.removeClass('d-none');
        });

        $.ajax({
            url: '{{ route("process.images") }}',
            method: 'GET',
            data: {
                urls: imageUrls
            },
            beforeSend: function() {
                $('.ai-btn-insert').prop("disabled",true);
                $('.ai-btn-insert').html(`{{translate('Insert')}}`)
                $('.ai-btn-insert').html(`{{translate('Insert')}}<div class="spinner-border spinner-border-sm text-white" role="status">
                                        <span class="visually-hidden"></span>
                                    </div>`)

            },
            success: function(response) {
                const files = response.map(function(item, index) {
                    if (item.error) {
                        console.error('Error for image:', item.url, item.error);
                        return null;
                    }

                    var binary = atob(item.content);
                    var array = new Uint8Array(binary.length);
                    for (var i = 0; i < binary.length; i++) {
                        array[i] = binary.charCodeAt(i);
                    }

                    var blob = new Blob([array], { type: item.content_type });

                    let fileExtension = 'jpg';
                    let mimeType = item.content_type || 'image/jpeg';

                    switch (item.content_type) {
                        case 'image/png':
                            fileExtension = 'png';
                            mimeType = 'image/png';
                            break;
                        case 'image/jpeg':
                        case 'image/jpg':
                            fileExtension = 'jpg';
                            mimeType = 'image/jpeg';
                            break;
                        case 'image/gif':
                            fileExtension = 'gif';
                            mimeType = 'image/gif';
                            break;
                        case 'image/webp':
                            fileExtension = 'webp';
                            mimeType = 'image/webp';
                            break;
                        default:
                            if (!item.content_type) {
                                console.warn(`Unknown MIME type for ${item.url}, defaulting to image/jpeg`);
                            }
                    }

                    let fileName = item.filename;
                    if (!fileName.includes('.')) {
                        fileName = `image-${index + 1}.${fileExtension}`;
                    } else if (!fileName.endsWith(`.${fileExtension}`)) {
                        fileName = fileName.replace(/\.[^/.]+$/, `.${fileExtension}`);
                    }

                    return new File([blob], fileName, { type: mimeType });
                }).filter(file => file !== null);

                if (files.length > 0) {
                    const dataTransfer = new DataTransfer();
                    files.forEach(file => dataTransfer.items.add(file));
                    $fileInput[0].files = dataTransfer.files;
                    $fileInput.trigger('change');
                } else {
                    console.error('No valid images processed');
                    $('.ai-btn-insert').prop("disabled",false);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error processing images:', error);
                $('.ai-btn-insert').prop("disabled",false);
            },
            complete: function() {
                $modal.modal('hide');
                $modal.on('hidden.bs.modal', function() {
                    $fileInput.focus();
                    $(this).off('hidden.bs.modal');
                });
                $('.ai-btn-insert').prop("disabled",false);
                $('.ai-btn-insert').html(`{{translate('Insert')}}`)
            },
        });
    });

    $(document).on('click', '.download-image', function(e) {
        e.preventDefault();

        const modalName = $(this).data('modal');
        const $modal = $(`#${modalName}`);

        const imageUrls = $modal.find('.image-check:checked').map(function() {
            return $(this).val();
        }).get();

        if (!imageUrls || imageUrls.length === 0) {
            console.log('Error: No image URLs found in #image_urls');
            return;
        }

        $.ajax({
            url: '{{ route("process.images") }}',
            method: 'GET',
            data: {
                urls: imageUrls
            },

            beforeSend: function() {
                $('.ai-btn-download').prop("disabled",true);
                $('.ai-btn-download').html(`{{translate('Download')}}`)
                $('.ai-btn-download').html(`{{translate('Download')}}<div class="spinner-border spinner-border-sm text-white" role="status">
                                        <span class="visually-hidden"></span>
                                    </div>`)

            },
            success: function(response) {
                response.forEach(function(item, index) {
                    if (item.error) {
                        console.error('Error for image:', item.url, item.error);
                        return;
                    }

                    var binary = atob(item.content);
                    var array = new Uint8Array(binary.length);
                    for (var i = 0; i < binary.length; i++) {
                        array[i] = binary.charCodeAt(i);
                    }

                    var blob = new Blob([array], { type: item.content_type });
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);

                    link.download = item.filename;

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    window.URL.revokeObjectURL(link.href);
                });
            },
            error: function(xhr, status, error) {
                console.error('Error downloading images:', error);
            },
            complete: function() {
                $('.ai-btn-download').prop("disabled",false);
                $('.ai-btn-download').html(`{{translate('Download')}}`)
            },

        });
    });

    $(document).on('input', '#image-search', function (e) {

        const modalName = $(this).data('modal');
        const $modal = $(`#${modalName}`);

        const searchTerm    = $(this).val().toLowerCase();
        const $imageContent = $modal.find('#image-content');

        $modal.find('.image-card-container').each(function () {
            const imageName = $(this).data('name').toLowerCase();
            if (imageName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });


        const visibleCards = $modal.find('.image-card-container:visible').length;

        $imageContent.find('.no-results-message').remove();

        if (visibleCards === 0) {
            $imageContent.append('<div class="no-results-message text-center text-muted mt-3">No results found</div>');
        }


    });


    $(document).on('click', '.insert-video', function (e) {
            e.preventDefault();

            if ($(this).hasClass('disabled')) return;
            $(this).addClass('disabled');

            const modalName = $(this).data('modal');
            const $modal    = $(`#${modalName}`);
            const $btn      = $(this);

            const videoUrls = $modal.find('.video-check:checked').map(function () {
                return $(this).val();
            }).get();

            if (!videoUrls || videoUrls.length === 0) {
                console.log('Error: No video URLs found');
                $(this).removeClass('disabled');
                return;
            }

            const $platformNote = $('.platform-note');
            const $socialPreviewBody = $('.social-preview-body');
            const $fileInput = $('#media-file');



            requestAnimationFrame(() => {
                $platformNote.addClass('d-none');
                $socialPreviewBody.removeClass('d-none');
            });

            $.ajax({
                url: '{{ route("process.videos") }}',
                method: 'POST',
                data: {
                    "_token": "{{ csrf_token()}}",
                    "urls" : videoUrls
                },
                beforeSend: function () {
                    $('.ai-btn-insert').prop("disabled",true);
                    $('.ai-btn-insert').html(`{{translate('Insert')}}`)
                    $('.ai-btn-insert').html(`{{translate('Insert')}}<div class="spinner-border spinner-border-sm text-white" role="status">
                                            <span class="visually-hidden"></span>
                                        </div>`)

                },
                success: function (response) {

                    const files = response.map(function (item, index) {
                        if (item.error) {
                            console.error('Error for video:', item.url, item.error);
                            return null;
                        }

                        var binary = atob(item.content);
                        var array = new Uint8Array(binary.length);
                        for (var i = 0; i < binary.length; i++) {
                            array[i] = binary.charCodeAt(i);
                        }

                        var blob = new Blob([array], { type: item.content_type });

                        let fileExtension = 'mp4';
                        let mimeType = item.content_type || 'video/mp4';

                        switch (item.content_type) {
                            case 'video/mp4':
                                fileExtension = 'mp4';
                                mimeType = 'video/mp4';
                                break;
                            case 'video/webm':
                                fileExtension = 'webm';
                                mimeType = 'video/webm';
                                break;
                            case 'video/ogg':
                                fileExtension = 'ogg';
                                mimeType = 'video/ogg';
                                break;
                            default:
                                if (!item.content_type) {
                                    console.warn(`Unknown MIME type for ${item.url}, defaulting to video/mp4`);
                                }
                        }

                        let fileName = item.filename;
                        if (!fileName.includes('.')) {
                            fileName = `video-${index + 1}.${fileExtension}`;
                        } else if (!fileName.endsWith(`.${fileExtension}`)) {
                            fileName = fileName.replace(/\.[^/.]+$/, `.${fileExtension}`);
                        }

                        return new File([blob], fileName, { type: mimeType });
                    }).filter(file => file !== null);

                    if (files.length > 0) {
                        const dataTransfer = new DataTransfer();
                        files.forEach(file => dataTransfer.items.add(file));
                        $fileInput[0].files = dataTransfer.files;
                        $fileInput.trigger('change');
                    } else {
                        console.error('No valid videos processed');
                        $('.ai-btn-insert').prop("disabled",false);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error processing videos:', error);
                    $('.ai-btn-insert').prop("disabled",false);
                },
                complete: function () {
                    $modal.modal('hide');
                    $modal.on('hidden.bs.modal', function () {
                        $fileInput.focus();
                        $(this).off('hidden.bs.modal');
                    });

                    $('.ai-btn-insert').prop("disabled",false);
                    $('.ai-btn-insert').html(`{{translate('Insert')}}`)

                },
            });
        });



    $(document).on('click', '.download-video', function (e) {
        e.preventDefault();

        const modalName = $(this).data('modal');
        const $modal = $(`#${modalName}`);
        const $btn   = $(this);

        const videoUrls = $modal.find('.video-check:checked').map(function () {
            return $(this).val();
        }).get();

        if (!videoUrls || videoUrls.length === 0) {
            console.log('Error: No video URLs found ');
            return;
        }

        $.ajax({
            url: '{{ route("process.videos") }}',
            method: 'POST',
            data: {
                "_token": "{{ csrf_token()}}",
                "urls" : videoUrls
            },

            beforeSend: function () {
                $('.ai-btn-download').prop("disabled",true);
                $('.ai-btn-download').html(`{{translate('Download')}}`)
                $('.ai-btn-download').html(`{{translate('Download')}}<div class="spinner-border spinner-border-sm text-white" role="status">
                                        <span class="visually-hidden"></span>
                                    </div>`)

            },
            success: function (response) {
                response.forEach(function (item, index) {
                    if (item.error) {
                        console.error('Error for video:', item.url, item.error);
                        return;
                    }

                    var binary = atob(item.content);
                    var array = new Uint8Array(binary.length);
                    for (var i = 0; i < binary.length; i++) {
                        array[i] = binary.charCodeAt(i);
                    }

                    var blob = new Blob([array], { type: item.content_type });
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);

                    link.download = item.filename;

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    window.URL.revokeObjectURL(link.href);
                });
            },
            error: function (xhr, status, error) {
                console.error('Error downloading images:', error);
                $('.ai-btn-download').prop("disabled",false);
                $('.ai-btn-download').html(`{{translate('Download')}}`)

            },
            complete: function () {
                $('.ai-btn-download').prop("disabled",false);
                $('.ai-btn-download').html(`{{translate('Download')}}`)

            },

        });
    });


    function formatState(state) {
        if (!state.id) {
            return state.text;
        }
        var baseUrl = $(state.element).data('image');
        var $state = $( '<span class="image-option"><img src="' + baseUrl + '" class="img-flag" /> ' + $('<div>').text(state.text).html() + '</span>' );
        return $state;
    }

    $('.profile-select').select2({
        templateResult: formatState,
        templateSelection: formatState,
    });

    function execCommandWithPreventDefault(command) {
        return function(event) {
            event.preventDefault();
            document.execCommand(command, false, null);
        };
    }


    var start = null;
    var end = null;

    function cb(start, end) {
        if (start) {
            const formattedDate = start.format('YYYY-MM-DDTHH:mm');
            const humanReadableDate = start.format('MMMM D, YYYY h:mm A');
            var cleanContent = DOMPurify.sanitize(humanReadableDate);
            $('#schedule_date_input').val(formattedDate);
            $('.show-date').html(`
                    <span class="pe-3">${cleanContent}
                    <i class="bi bi-x ps-2 fs-6 text--danger pointer  clear-input "></i></span>`);

        } else {
            $('#schedule_date_input').val('');
            $('.show-date').html('');
        }
    }


        $('#schedule_date_picker').daterangepicker(
            {
                singleDatePicker: true,
                timePicker: true,
                timePicker24Hour: true,
                showDropdowns: true,
                locale: {
                    format: 'YYYY-MM-DDTHH:mm'
                }
            },
            cb
        );

        $('#schedule_date_picker').on('apply.daterangepicker', function (ev, picker) {
            cb(picker.startDate, picker.endDate);
        });

        $(document).on('click', '.clear-input',function(e){
            e.preventDefault()
            cb(null, null);
        })
        cb(start, end);


    const selectTwo = document.querySelector(".select-two");
  $(document).ready(function () {
    if (selectTwo) {
      $(selectTwo).select2(
        {
            placeholder: "Select a state",
        }
      );
    }
  });

//   Gallery
function updateActionButtons() {
        const checkedCount = document.querySelectorAll('.gallery-checkbox:checked').length;
        const actionButtons = document.getElementById('actionButtons');

        if (checkedCount > 0) {
            actionButtons.classList.remove('d-none');
            actionButtons.classList.add('d-block');
        } else {
            actionButtons.classList.remove('d-block');
            actionButtons.classList.add('d-none');
        }
        }

        document.getElementById('checkAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.gallery-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateActionButtons();
        });

        document.querySelectorAll('.gallery-item').forEach(item => {
        item.addEventListener('click', function(e) {
            if (e.target.tagName.toLowerCase() !== 'input') {
            const checkbox = this.querySelector('.gallery-checkbox');
            checkbox.checked = !checkbox.checked;
            updateActionButtons();
            }
        });
        });

        document.querySelectorAll('.gallery-checkbox').forEach(checkbox => {
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
            updateActionButtons();
        });
        });

})(jQuery);

// Store Modal Functions
function openStoreModal(platform) {
    const modal = document.getElementById('storeModal');
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function closeStoreModal() {
    const modal = document.getElementById('storeModal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
    }
}

function copyStoreUrl(url) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(function() {
            showToast('Store URL copied to clipboard!', 'success');
        }).catch(function(err) {
            fallbackCopyTextToClipboard(url);
        });
    } else {
        fallbackCopyTextToClipboard(url);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('Store URL copied to clipboard!', 'success');
        } else {
            showToast('Failed to copy URL', 'error');
        }
    } catch (err) {
        showToast('Failed to copy URL', 'error');
    }

    document.body.removeChild(textArea);
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 99999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('storeModal');
    if (modal && event.target === modal) {
        closeStoreModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeStoreModal();
    }
});

// Product Creation Functionality
$(document).ready(function() {
    // Check if product data exists and show enhanced AI options
    @if(session('product_for_posting.use_enhanced_ai'))
        $('#enhancedAiOptions').show();

        // Pre-fill product data if available
        var productData = @json(session('product_for_posting'));
        if (productData) {
            window.productForPosting = productData;
        }
    @endif

    // Enhanced AI Content Generation
    $('.ai-content-modal').on('click', function (e) {
        e.preventDefault();
        var modal = $('#aiModal');
        var inputText = $('#inputText').val();

        modal.find('.ai-content-form')[0].reset();

        // Check if we should use enhanced AI
        var useEnhancedAI = window.productForPosting || $('#post_to_website_first').is(':checked');

        if (useEnhancedAI) {
            modal.find('.ai-modal-title').html("{{translate('Generate Enhanced AI Content')}}");
            // Add enhanced AI indicator
            if (!modal.find('.enhanced-ai-indicator').length) {
                modal.find('.modal-header').append('<span class="enhanced-ai-indicator badge bg-success ms-2"><i class="bi bi-robot"></i> Enhanced AI</span>');
            }

            // Add enhanced AI options to the modal
            addEnhancedAiOptionsToModal(modal);

            // Pre-fill product data if available
            if (window.productForPosting) {
                prefillProductDataInModal(modal);
            }
        } else {
            modal.find('.ai-modal-title').html("{{translate('Generate Content')}}");
            modal.find('.enhanced-ai-indicator').remove();
            modal.find('.enhanced-ai-options-section').remove();
        }

        // Pre-fill with existing content if available
        if (inputText) {
            modal.find('#promptPreview').val(inputText);
        }

        modal.modal('show');
    });

    // Handle enhanced AI content generation
    $(document).on('submit', '.ai-content-form', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var useEnhancedAI = window.productForPosting || $('#post_to_website_first').is(':checked');

        if (useEnhancedAI) {
            generateEnhancedContent(formData);
        } else {
            // Use existing standard content generation
            generateStandardContent(formData);
        }
    });

    // Override the default AI form submission for enhanced functionality
    $(document).off('submit', '.ai-content-form').on('submit', '.ai-content-form', function(e) {
        e.preventDefault();

        var $form = $(this);
        var formData = new FormData(this);
        var useEnhancedAI = window.productForPosting || $('#post_to_website_first').is(':checked');

        // Check if we have product data or enhanced AI is requested
        if (useEnhancedAI && (window.productForPosting || $('#post_to_website_first').is(':checked'))) {
            generateEnhancedContentFromForm($form, formData);
        } else {
            // Use the original AI generation logic
            generateStandardContentFromForm($form, formData);
        }
    });

    // Enhanced content generation from AI form
    function generateEnhancedContentFromForm($form, formData) {
        var productData = window.productForPosting || {};
        var selectedPlatforms = [];

        // Get selected platforms
        $('input[name="account_id[]"]:checked').each(function() {
            var platformSlug = $(this).closest('.account-item-enhanced').find('.account-label-enhanced').data('platform');
            if (platformSlug) {
                selectedPlatforms.push(platformSlug);
            }
        });

        var enhancedData = {
            product_name: productData.name || $('#product_name').val() || 'Sample Product',
            product_description: productData.description || $('#product_description').val() || formData.get('custom_prompt_input') || 'Amazing product description',
            product_price: productData.price || $('#product_price').val(),
            product_category: productData.category || $('#product_category').val(),
            product_image: productData.image || $('#product_image').val(),
            target_platforms: selectedPlatforms,
            post_to_website_first: $('#modal_post_to_website_first').is(':checked') || $('#post_to_website_first').is(':checked'),
            language: formData.get('language') || 'English',
            tone: formData.get('content_tone') || 'Professional',
            include_hashtags: $('#modal_include_hashtags').is(':checked') || $('#include_hashtags').is(':checked'),
            include_emojis: $('#modal_include_emojis').is(':checked') || $('#include_emojis').is(':checked'),
            include_cta: $('#modal_include_cta').is(':checked') || $('#include_cta').is(':checked'),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: '{{route("user.ai.enhanced.product.content")}}',
            method: 'POST',
            data: enhancedData,
            beforeSend: function() {
                $form.find('button[type="submit"]').prop('disabled', true).html('<i class="bi bi-arrow-clockwise spin"></i> {{translate("Generating Enhanced Content...")}}');
            },
            success: function(response) {
                if (response.success) {
                    handleEnhancedContentResponse(response.data);
                    $('#aiModal').modal('hide');
                    toastr.success(response.message || '{{translate("Enhanced content generated successfully!")}}');
                } else {
                    toastr.error(response.message || '{{translate("Failed to generate enhanced content")}}');
                }
            },
            error: function(xhr) {
                var errorMessage = '{{translate("An error occurred while generating enhanced content")}}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                toastr.error(errorMessage);
            },
            complete: function() {
                $form.find('button[type="submit"]').prop('disabled', false).html('{{translate("Generate")}} <i class="bi bi-send generate-icon-btn"></i>');
            }
        });
    }

    // Standard content generation from AI form
    function generateStandardContentFromForm($form, formData) {
        var selectedPlatforms = [];

        // Get selected platforms
        $('input[name="account_id[]"]:checked').each(function() {
            var platformSlug = $(this).closest('.account-item-enhanced').find('.account-label-enhanced').data('platform');
            if (platformSlug) {
                selectedPlatforms.push(platformSlug);
            }
        });

        var standardData = {
            post_content: formData.get('custom_prompt_input') || $('#inputText').val(),
            target_platforms: selectedPlatforms,
            language: formData.get('language') || 'English',
            tone: formData.get('content_tone') || 'Professional',
            include_hashtags: $('#include_hashtags').is(':checked'),
            include_emojis: $('#include_emojis').is(':checked'),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: '{{route("user.ai.enhanced.standard.content")}}',
            method: 'POST',
            data: standardData,
            beforeSend: function() {
                $form.find('button[type="submit"]').prop('disabled', true).html('<i class="bi bi-arrow-clockwise spin"></i> {{translate("Generating Content...")}}');
            },
            success: function(response) {
                if (response.success) {
                    handleStandardContentResponse(response.data);
                    $('#aiModal').modal('hide');
                    toastr.success(response.message || '{{translate("Content generated successfully!")}}');
                } else {
                    toastr.error(response.message || '{{translate("Failed to generate content")}}');
                }
            },
            error: function(xhr) {
                var errorMessage = '{{translate("An error occurred while generating content")}}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                toastr.error(errorMessage);
            },
            complete: function() {
                $form.find('button[type="submit"]').prop('disabled', false).html('{{translate("Generate")}} <i class="bi bi-send generate-icon-btn"></i>');
            }
        });
    }

    // Load categories when product tab is shown
    $('#product-tab').on('shown.bs.tab', function() {
        loadCategories();
    });

    // Enhanced AI Content Generation Function
    function generateEnhancedContent(formData) {
        var productData = window.productForPosting || {};
        var selectedPlatforms = [];

        // Get selected platforms
        $('input[name="account_id[]"]:checked').each(function() {
            var platformSlug = $(this).closest('.account-item-enhanced').find('.account-label-enhanced').data('platform');
            if (platformSlug) {
                selectedPlatforms.push(platformSlug);
            }
        });

        var enhancedData = {
            product_name: productData.name || $('#product_name').val() || 'Sample Product',
            product_description: productData.description || $('#product_description').val() || 'Amazing product description',
            product_price: productData.price || $('#product_price').val(),
            product_category: productData.category || $('#product_category').val(),
            product_image: productData.image || $('#product_image').val(),
            target_platforms: selectedPlatforms,
            post_to_website_first: $('#post_to_website_first').is(':checked'),
            language: formData.get('language') || 'English',
            tone: formData.get('tone') || 'Professional',
            include_hashtags: $('#include_hashtags').is(':checked'),
            include_emojis: $('#include_emojis').is(':checked'),
            include_cta: $('#include_cta').is(':checked'),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: '{{route("user.ai.enhanced.product.content")}}',
            method: 'POST',
            data: enhancedData,
            beforeSend: function() {
                $('.ai-content-form button[type="submit"]').prop('disabled', true).html('<i class="bi bi-arrow-clockwise spin"></i> {{translate("Generating...")}}');
            },
            success: function(response) {
                if (response.success) {
                    handleEnhancedContentResponse(response.data);
                    $('#aiModal').modal('hide');
                    toastr.success(response.message || '{{translate("Content generated successfully!")}}');
                } else {
                    toastr.error(response.message || '{{translate("Failed to generate content")}}');
                }
            },
            error: function(xhr) {
                var errorMessage = '{{translate("An error occurred while generating content")}}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                toastr.error(errorMessage);
            },
            complete: function() {
                $('.ai-content-form button[type="submit"]').prop('disabled', false).html('{{translate("Generate Content")}}');
            }
        });
    }

    // Standard AI Content Generation Function
    function generateStandardContent(formData) {
        var selectedPlatforms = [];

        // Get selected platforms
        $('input[name="account_id[]"]:checked').each(function() {
            var platformSlug = $(this).closest('.account-item-enhanced').find('.account-label-enhanced').data('platform');
            if (platformSlug) {
                selectedPlatforms.push(platformSlug);
            }
        });

        var standardData = {
            post_content: $('#inputText').val(),
            target_platforms: selectedPlatforms,
            language: formData.get('language') || 'English',
            tone: formData.get('tone') || 'Professional',
            include_hashtags: $('#include_hashtags').is(':checked'),
            include_emojis: $('#include_emojis').is(':checked'),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: '{{route("user.ai.enhanced.standard.content")}}',
            method: 'POST',
            data: standardData,
            beforeSend: function() {
                $('.ai-content-form button[type="submit"]').prop('disabled', true).html('<i class="bi bi-arrow-clockwise spin"></i> {{translate("Generating...")}}');
            },
            success: function(response) {
                if (response.success) {
                    handleStandardContentResponse(response.data);
                    $('#aiModal').modal('hide');
                    toastr.success(response.message || '{{translate("Content generated successfully!")}}');
                } else {
                    toastr.error(response.message || '{{translate("Failed to generate content")}}');
                }
            },
            error: function(xhr) {
                var errorMessage = '{{translate("An error occurred while generating content")}}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                toastr.error(errorMessage);
            },
            complete: function() {
                $('.ai-content-form button[type="submit"]').prop('disabled', false).html('{{translate("Generate Content")}}');
            }
        });
    }

    // Handle Enhanced Content Response
    function handleEnhancedContentResponse(data) {
        var content = data.content || {};
        var meta = data.meta || {};

        // Store platform-specific content for later use
        window.platformContent = content;
        window.productMeta = meta;

        // Update main caption with the first platform's content
        var firstPlatform = Object.keys(content)[0];
        if (firstPlatform && content[firstPlatform]) {
            var caption = content[firstPlatform].caption || '';
            $('#inputText').val(caption);
            $('.caption-text').html(caption.replace(/(#\w+)/g, '<span class="hashtag">$1</span>'));
        }

        // Update product URL if available
        if (meta.product_url) {
            $('#link').val(meta.product_url);
            $('.caption-link').html('<a href="' + meta.product_url + '" target="_blank">' + meta.product_url + '</a>');
        }

        // Add platform-specific content indicators
        addPlatformContentIndicators(content);

        // Update platform previews with specific content
        updatePlatformPreviews(content, meta);

        // Show preview
        $('.platform-note').addClass('d-none');
        $('.social-preview-body').removeClass('d-none');

        // Show success message with platform details
        var platformCount = Object.keys(content).length;
        toastr.success('Enhanced AI content generated for ' + platformCount + ' platform(s)!');
    }

    // Add platform-specific content indicators
    function addPlatformContentIndicators(content) {
        $('.account-item-enhanced').each(function() {
            var $item = $(this);
            var platformSlug = $item.find('.account-label-enhanced').data('platform');

            // Remove existing indicators
            $item.find('.platform-content-indicator').remove();

            if (content[platformSlug]) {
                var platformData = content[platformSlug];
                var indicator = '<div class="platform-content-indicator mt-2">';

                // URL handling indicator
                if (platformData.url_handling === 'bio_link') {
                    indicator += '<span class="badge bg-warning text-dark"><i class="bi bi-link-45deg"></i> Bio Link</span>';
                } else if (platformData.url_handling === 'direct') {
                    indicator += '<span class="badge bg-success"><i class="bi bi-link"></i> Direct URL</span>';
                }

                // Hashtag count
                if (platformData.hashtags && platformData.hashtags.length > 0) {
                    indicator += ' <span class="badge bg-info">' + platformData.hashtags.length + ' hashtags</span>';
                }

                // Special notes
                if (platformData.note) {
                    indicator += '<br><small class="text-muted">' + platformData.note + '</small>';
                }

                indicator += '</div>';
                $item.append(indicator);
            }
        });
    }

    // Update platform previews with specific content
    function updatePlatformPreviews(content, meta) {
        // Update each platform's preview if it exists
        Object.keys(content).forEach(function(platform) {
            var platformData = content[platform];
            var $preview = $('.social-preview-body[data-platform="' + platform + '"]');

            if ($preview.length > 0) {
                // Update caption
                $preview.find('.caption-text').html(
                    platformData.caption.replace(/(#\w+)/g, '<span class="hashtag">$1</span>')
                );

                // Update URL display based on platform
                var $linkElement = $preview.find('.caption-link');
                if (platformData.url_handling === 'bio_link') {
                    $linkElement.html('<small class="text-muted">🔗 Link in bio</small>');
                } else if (platformData.product_url) {
                    $linkElement.html('<a href="' + platformData.product_url + '" target="_blank">' + platformData.product_url + '</a>');
                }
            }
        });
    }

    // Handle Standard Content Response
    function handleStandardContentResponse(data) {
        var content = data.content || {};

        // Update main caption
        var firstPlatform = Object.keys(content)[0];
        if (firstPlatform && content[firstPlatform]) {
            var caption = content[firstPlatform].caption || '';
            $('#inputText').val(caption);
            $('.caption-text').html(caption.replace(/(#\w+)/g, '<span class="hashtag">$1</span>'));
        }

        // Store platform-specific content
        window.platformContent = content;

        // Show preview
        $('.platform-note').addClass('d-none');
        $('.social-preview-body').removeClass('d-none');
    }

    // Platform-specific content switching
    $(document).on('change', 'input[name="account_id[]"]', function() {
        if (window.platformContent) {
            updateContentForSelectedPlatforms();
        }
    });

    // Update content based on selected platforms
    function updateContentForSelectedPlatforms() {
        var selectedPlatforms = [];
        var primaryContent = '';

        // Get selected platforms
        $('input[name="account_id[]"]:checked').each(function() {
            var platformSlug = $(this).closest('.account-item-enhanced').find('.account-label-enhanced').data('platform');
            if (platformSlug && window.platformContent[platformSlug]) {
                selectedPlatforms.push(platformSlug);
            }
        });

        // Use the first selected platform's content as primary
        if (selectedPlatforms.length > 0) {
            var primaryPlatform = selectedPlatforms[0];
            primaryContent = window.platformContent[primaryPlatform].caption || '';

            // Update main caption
            $('#inputText').val(primaryContent);
            $('.caption-text').html(primaryContent.replace(/(#\w+)/g, '<span class="hashtag">$1</span>'));

            // Show platform-specific warnings
            showPlatformWarnings(selectedPlatforms);
        }
    }

    // Show platform-specific warnings and notes
    function showPlatformWarnings(platforms) {
        // Remove existing warnings
        $('.platform-warning').remove();

        var warnings = [];

        platforms.forEach(function(platform) {
            var platformData = window.platformContent[platform];
            if (platformData && platformData.note) {
                warnings.push({
                    platform: platform,
                    note: platformData.note,
                    type: platformData.url_handling === 'bio_link' ? 'warning' : 'info'
                });
            }
        });

        // Display warnings
        if (warnings.length > 0) {
            var warningHtml = '<div class="platform-warning mt-3">';
            warnings.forEach(function(warning) {
                var alertClass = warning.type === 'warning' ? 'alert-warning' : 'alert-info';
                warningHtml += '<div class="alert ' + alertClass + ' alert-sm">';
                warningHtml += '<strong>' + warning.platform.toUpperCase() + ':</strong> ' + warning.note;
                warningHtml += '</div>';
            });
            warningHtml += '</div>';

            $('.caption-wrapper').append(warningHtml);
        }
    }

    // Enhanced platform content preview
    function showPlatformContentPreview() {
        if (!window.platformContent) return;

        var previewHtml = '<div class="platform-content-preview mt-3">';
        previewHtml += '<h6>Platform-Specific Content Preview:</h6>';

        Object.keys(window.platformContent).forEach(function(platform) {
            var platformData = window.platformContent[platform];
            previewHtml += '<div class="platform-preview-item mb-2">';
            previewHtml += '<strong>' + platform.toUpperCase() + ':</strong><br>';
            previewHtml += '<div class="preview-content">' + platformData.caption.substring(0, 100) + '...</div>';

            if (platformData.url_handling === 'bio_link') {
                previewHtml += '<small class="text-warning">⚠️ URL will be referenced as "Link in bio"</small>';
            }

            previewHtml += '</div>';
        });

        previewHtml += '</div>';

        // Remove existing preview and add new one
        $('.platform-content-preview').remove();
        $('.caption-wrapper').append(previewHtml);
    }

    // Add enhanced AI options to modal
    function addEnhancedAiOptionsToModal(modal) {
        // Remove existing enhanced options
        modal.find('.enhanced-ai-options-section').remove();

        var enhancedOptionsHtml = `
            <div class="enhanced-ai-options-section mt-4 p-3 bg-light rounded">
                <h6 class="mb-3"><i class="bi bi-robot"></i> Enhanced AI Options</h6>

                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="modal_include_hashtags" checked>
                            <label class="form-check-label" for="modal_include_hashtags">
                                Include Hashtags
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="modal_include_emojis" checked>
                            <label class="form-check-label" for="modal_include_emojis">
                                Include Emojis
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="modal_include_cta" checked>
                            <label class="form-check-label" for="modal_include_cta">
                                Include Call-to-Action
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row g-3 mt-2">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="modal_post_to_website_first">
                            <label class="form-check-label" for="modal_post_to_website_first">
                                Post to website first
                            </label>
                            <small class="text-muted d-block">Create product in Nazmart before generating content</small>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3 mb-0">
                    <small><i class="bi bi-info-circle"></i> Enhanced AI will generate platform-specific content optimized for each social media platform.</small>
                </div>
            </div>
        `;

        // Insert after the advanced options
        modal.find('.advnced-option-card').after(enhancedOptionsHtml);
    }

    // Pre-fill product data in modal
    function prefillProductDataInModal(modal) {
        if (window.productForPosting) {
            var productData = window.productForPosting;
            var promptText = `Create engaging social media content for: ${productData.name}\n\nDescription: ${productData.description}`;

            if (productData.price) {
                promptText += `\nPrice: $${productData.price}`;
            }

            modal.find('#promptPreview').val(promptText);
        }
    }

    function loadCategories() {
        $.ajax({
            url: '{{route("user.product.categories")}}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const categorySelect = $('#category');
                    categorySelect.empty().append('<option value="">{{translate("Select Category")}}</option>');

                    response.data.forEach(function(category) {
                        categorySelect.append(`<option value="${category.id}">${category.name}</option>`);
                    });
                }
            },
            error: function() {
                console.log('Failed to load categories');
            }
        });
    }

    // Image upload handling for product
    $('#imageUploadArea').on('click', function() {
        $('#image').click();
    });

    $('#image').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#imageUploadArea').hide();
            };
            reader.readAsDataURL(file);
        }
    });

    $('#removeImage').on('click', function() {
        $('#image').val('');
        $('#imagePreview').hide();
        $('#imageUploadArea').show();
    });

    // Drag and drop functionality for product
    $('#imageUploadArea').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('#imageUploadArea').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('#imageUploadArea').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#image')[0].files = files;
            $('#image').trigger('change');
        }
    });

    // Product form submission
    $('#productForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Show loading state
        $('#loadingOverlay').show();
        $('.product-form-container').addClass('loading');
        $('#submitBtn').prop('disabled', true);

        $.ajax({
            url: '{{route("user.product.store")}}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    if (response.redirect_to_post && response.product_url) {
                        // Switch to post tab and pre-fill with product data
                        $('#post-tab').tab('show');

                        // Pre-fill post content with product information
                        setTimeout(function() {
                            const productText = `Check out my new product: ${response.data.name}\n\n${response.data.description}\n\nPrice: $${response.data.price}\n\n${response.product_url}`;

                            // Find the first active textarea and fill it
                            const activeTextarea = $('.tab-pane.active textarea[name="content"]').first();
                            if (activeTextarea.length) {
                                activeTextarea.val(productText);
                            }

                            showAlert('success', 'Product created successfully! Post content has been pre-filled.');
                        }, 500);
                    } else {
                        showAlert('success', response.message);
                    }

                    // Reset form
                    $('#productForm')[0].reset();
                    $('#imagePreview').hide();
                    $('#imageUploadArea').show();
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr) {
                let message = 'An error occurred while creating the product.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert('danger', message);
            },
            complete: function() {
                // Hide loading state
                $('#loadingOverlay').hide();
                $('.product-form-container').removeClass('loading');
                $('#submitBtn').prop('disabled', false);
            }
        });
    });

    function showAlert(type, message) {
        const alertHtml = `<div class="alert alert-${type}">${message}</div>`;
        $('.product-form-container').prepend(alertHtml);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});

</script>
@endpush


