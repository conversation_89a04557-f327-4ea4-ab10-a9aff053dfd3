@extends('admin.layouts.master')

@section('content')
    <div class="row g-4">
        <div class="col-xl-12">
            <div class="i-card-md">
                <div class="card--header">
                    <h4 class="card-title">
                        {{translate('Create New Store')}}
                    </h4>
                    <div class="action">
                        <button type="button" id="testConnectionBtn" class="i-btn btn--sm success">
                            <i class="las la-plug"></i> {{translate('Test Connection')}}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="storeCreationForm">
                        @csrf
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-inner">
                                    <label for="name">
                                        {{translate('Owner Name')}} <small class="text-danger">*</small>
                                    </label>
                                    <input placeholder="{{translate('Enter owner full name')}}" 
                                           id="name" 
                                           required 
                                           type="text" 
                                           name="name" 
                                           value='{{old("name")}}'>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-inner">
                                    <label for="email">
                                        {{translate('Email Address')}} <small class="text-danger">*</small>
                                    </label>
                                    <input placeholder="{{translate('Enter email address')}}" 
                                           id="email" 
                                           required 
                                           type="email" 
                                           name="email" 
                                           value='{{old("email")}}'>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-inner">
                                    <label for="store_name">
                                        {{translate('Store Name/Subdomain')}} <small class="text-danger">*</small>
                                    </label>
                                    <input placeholder="{{translate('Enter store name (will be used as subdomain)')}}" 
                                           id="store_name" 
                                           required 
                                           type="text" 
                                           name="store_name" 
                                           value='{{old("store_name")}}'
                                           pattern="[a-zA-Z0-9\-_]+"
                                           title="Only letters, numbers, hyphens, and underscores are allowed">
                                    <small class="text-muted">{{translate('Only letters, numbers, hyphens, and underscores allowed')}}</small>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-inner">
                                    <label for="phone">
                                        {{translate('Phone Number')}} <small class="text-danger">*</small>
                                    </label>
                                    <input placeholder="{{translate('Enter phone number')}}" 
                                           id="phone" 
                                           required 
                                           type="text" 
                                           name="phone" 
                                           value='{{old("phone")}}'>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-inner">
                                    <label for="category">
                                        {{translate('Store Category')}}
                                    </label>
                                    <select name="category" id="category" class="form-control">
                                        <option value="">{{translate('Select Category (Optional)')}}</option>
                                        <option value="fashion">{{translate('Fashion & Clothing')}}</option>
                                        <option value="electronics">{{translate('Electronics')}}</option>
                                        <option value="home_garden">{{translate('Home & Garden')}}</option>
                                        <option value="health_beauty">{{translate('Health & Beauty')}}</option>
                                        <option value="sports_outdoors">{{translate('Sports & Outdoors')}}</option>
                                        <option value="books_media">{{translate('Books & Media')}}</option>
                                        <option value="toys_games">{{translate('Toys & Games')}}</option>
                                        <option value="automotive">{{translate('Automotive')}}</option>
                                        <option value="food_beverage">{{translate('Food & Beverage')}}</option>
                                        <option value="jewelry_accessories">{{translate('Jewelry & Accessories')}}</option>
                                        <option value="other">{{translate('Other')}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-inner mt-4">
                            <button type="submit" class="i-btn btn--md btn--primary" id="submitBtn">
                                <i class="las la-plus"></i> {{translate('Create Store')}}
                            </button>
                            <button type="button" class="i-btn btn--md btn--secondary ms-2" onclick="resetForm()">
                                <i class="las la-redo"></i> {{translate('Reset')}}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Store Creation Result Modal -->
    <div class="modal fade" id="storeResultModal" tabindex="-1" aria-labelledby="storeResultModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="storeResultModalLabel">{{translate('Store Creation Result')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="storeResultContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{translate('Close')}}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-include')
<script nonce="{{ csp_nonce() }}">
    $(document).ready(function() {
        // Test connection functionality
        $('#testConnectionBtn').click(function() {
            const btn = $(this);
            const originalText = btn.html();
            
            btn.html('<i class="las la-spinner la-spin"></i> {{translate("Testing...")}}').prop('disabled', true);
            
            $.ajax({
                url: '{{route("admin.store.test.connection")}}',
                method: 'POST',
                data: {
                    _token: '{{csrf_token()}}'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    toastr.error(response?.message || '{{translate("Connection test failed")}}');
                },
                complete: function() {
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });

        // Store creation form submission
        $('#storeCreationForm').submit(function(e) {
            e.preventDefault();
            
            const form = $(this);
            const submitBtn = $('#submitBtn');
            const originalText = submitBtn.html();
            
            submitBtn.html('<i class="las la-spinner la-spin"></i> {{translate("Creating...")}}').prop('disabled', true);
            
            $.ajax({
                url: '{{route("admin.store.store")}}',
                method: 'POST',
                data: form.serialize(),
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        showStoreResult(response);
                        form[0].reset();
                    } else {
                        toastr.error(response.message);
                        if (response.errors) {
                            displayValidationErrors(response.errors);
                        }
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    toastr.error(response?.message || '{{translate("Store creation failed")}}');
                    if (response?.errors) {
                        displayValidationErrors(response.errors);
                    }
                },
                complete: function() {
                    submitBtn.html(originalText).prop('disabled', false);
                }
            });
        });
    });

    function showStoreResult(response) {
        let content = '<div class="alert alert-success">';
        content += '<h6>{{translate("Store Created Successfully!")}}</h6>';
        content += '<p>' + response.message + '</p>';
        
        if (response.data) {
            content += '<hr>';
            content += '<h6>{{translate("Store Details:")}}</h6>';
            content += '<ul class="list-unstyled">';
            
            if (response.data.store_id) {
                content += '<li><strong>{{translate("Store ID:")}} </strong>' + response.data.store_id + '</li>';
            }
            if (response.data.subdomain) {
                content += '<li><strong>{{translate("Subdomain:")}} </strong>' + response.data.subdomain + '</li>';
            }
            if (response.data.store_url) {
                content += '<li><strong>{{translate("Store URL:")}} </strong><a href="' + response.data.store_url + '" target="_blank">' + response.data.store_url + '</a></li>';
            }
            if (response.data.admin_url) {
                content += '<li><strong>{{translate("Admin URL:")}} </strong><a href="' + response.data.admin_url + '" target="_blank">' + response.data.admin_url + '</a></li>';
            }
            
            content += '</ul>';
        }
        
        content += '</div>';
        
        $('#storeResultContent').html(content);
        $('#storeResultModal').modal('show');
    }

    function displayValidationErrors(errors) {
        $.each(errors, function(field, messages) {
            const input = $('[name="' + field + '"]');
            input.addClass('is-invalid');
            
            // Remove existing error message
            input.siblings('.invalid-feedback').remove();
            
            // Add new error message
            input.after('<div class="invalid-feedback">' + messages[0] + '</div>');
        });
    }

    function resetForm() {
        $('#storeCreationForm')[0].reset();
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    }
</script>
@endpush
