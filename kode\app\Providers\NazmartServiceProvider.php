<?php

namespace App\Providers;

use App\Services\Nazmart\StoreCreationService;
use App\Services\Nazmart\ProductUploadService;
use App\Services\Nazmart\ProductURLService;
use Illuminate\Support\ServiceProvider;

/**
 * Nazmart Service Provider
 * 
 * Registers all Nazmart-related services and their dependencies
 */
class NazmartServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Nazmart services as singletons for better performance
        $this->app->singleton(StoreCreationService::class, function ($app) {
            return new StoreCreationService();
        });

        $this->app->singleton(ProductUploadService::class, function ($app) {
            return new ProductUploadService();
        });

        $this->app->singleton(ProductURLService::class, function ($app) {
            return new ProductURLService();
        });

        // Register aliases for backward compatibility if needed
        $this->app->alias(StoreCreationService::class, 'nazmart.store');
        $this->app->alias(ProductUploadService::class, 'nazmart.product.upload');
        $this->app->alias(ProductURLService::class, 'nazmart.product.url');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Any bootstrapping logic can go here
        // For example, configuration publishing, event listeners, etc.
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [
            StoreCreationService::class,
            ProductUploadService::class,
            ProductURLService::class,
            'nazmart.store',
            'nazmart.product.upload',
            'nazmart.product.url',
        ];
    }
}
