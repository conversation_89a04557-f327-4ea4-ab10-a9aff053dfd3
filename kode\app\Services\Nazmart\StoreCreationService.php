<?php

namespace App\Services\Nazmart;

/**
 * Nazmart Store Creation Service
 * 
 * Handles all store creation and management operations with Nazmart API
 * including store creation, store information retrieval, and store validation.
 */
class StoreCreationService extends BaseNazmartService
{
    /**
     * Create a new store in Nazmart
     *
     * @param array $storeData
     * @return array
     */
    public function createStore(array $storeData): array
    {
        // Validate required fields
        $validation = $this->validateRequiredFields($storeData, [
            'name', 'email', 'store_name', 'phone'
        ]);

        if ($validation) {
            return $validation;
        }

        // Prepare data payload
        $payload = [
            'name' => $storeData['name'],
            'email' => $storeData['email'],
            'store_name' => $storeData['store_name'],
            'phone' => $storeData['phone'],
            'category' => $storeData['category'] ?? null
        ];

        return $this->makePostRequest('create-store', $payload);
    }

    /**
     * Get store information from Nazmart
     *
     * @param string $storeId
     * @return array
     */
    public function getStoreInfo(string $storeId): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        return $this->makeGetRequest("store/{$storeId}");
    }

    /**
     * Get store information by slug
     *
     * @param string $storeSlug
     * @return array
     */
    public function getStoreBySlug(string $storeSlug): array
    {
        if (empty($storeSlug)) {
            return [
                'success' => false,
                'message' => 'Store slug is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_SLUG'
            ];
        }

        return $this->makeGetRequest('store', ['slug' => $storeSlug]);
    }

    /**
     * Update store information
     *
     * @param string $storeId
     * @param array $updateData
     * @return array
     */
    public function updateStore(string $storeId, array $updateData): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        return $this->makePostRequest("store/{$storeId}/update", $updateData);
    }

    /**
     * Check if store exists
     *
     * @param string $identifier Can be store ID, email, or store name
     * @param string $type Type of identifier: 'id', 'email', 'store_name'
     * @return array
     */
    public function checkStoreExists(string $identifier, string $type = 'id'): array
    {
        if (empty($identifier)) {
            return [
                'success' => false,
                'message' => 'Store identifier is required',
                'data' => null,
                'error_code' => 'MISSING_IDENTIFIER'
            ];
        }

        $params = [$type => $identifier];
        return $this->makeGetRequest('store/check', $params);
    }

    /**
     * Get store categories available for selection
     *
     * @return array
     */
    public function getStoreCategories(): array
    {
        return $this->makeGetRequest('store/categories');
    }

    /**
     * Validate store data before creation
     *
     * @param array $storeData
     * @return array
     */
    public function validateStoreData(array $storeData): array
    {
        $errors = [];

        // Required field validation
        $requiredFields = ['name', 'email', 'store_name', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($storeData[$field])) {
                $errors[$field] = "The {$field} field is required.";
            }
        }

        // Email validation
        if (!empty($storeData['email']) && !filter_var($storeData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'The email must be a valid email address.';
        }

        // Store name validation (alphanumeric and spaces only)
        if (!empty($storeData['store_name']) && !preg_match('/^[a-zA-Z0-9\s]+$/', $storeData['store_name'])) {
            $errors['store_name'] = 'The store name may only contain letters, numbers, and spaces.';
        }

        // Phone validation (basic)
        if (!empty($storeData['phone']) && !preg_match('/^[\+]?[0-9\-\(\)\s]+$/', $storeData['phone'])) {
            $errors['phone'] = 'The phone number format is invalid.';
        }

        if (!empty($errors)) {
            return [
                'success' => false,
                'message' => 'Validation failed',
                'data' => null,
                'errors' => $errors,
                'error_code' => 'VALIDATION_FAILED'
            ];
        }

        return [
            'success' => true,
            'message' => 'Validation passed',
            'data' => $storeData
        ];
    }

    /**
     * Generate store URL based on store data
     *
     * @param array $storeData
     * @return string
     */
    public function generateStoreUrl(array $storeData): string
    {
        $baseUrl = config('settings.social_platforms.nazmart.credential.base_url', 'https://vendora.com');
        
        if (isset($storeData['store_slug'])) {
            return rtrim($baseUrl, '/') . '/stores/' . $storeData['store_slug'];
        }

        if (isset($storeData['subdomain'])) {
            return rtrim($baseUrl, '/') . '/stores/' . $storeData['subdomain'];
        }

        if (isset($storeData['store_name'])) {
            $slug = $this->generateSlugFromName($storeData['store_name']);
            return rtrim($baseUrl, '/') . '/stores/' . $slug;
        }

        return $baseUrl;
    }

    /**
     * Generate a URL-friendly slug from store name
     *
     * @param string $name
     * @return string
     */
    private function generateSlugFromName(string $name): string
    {
        // Convert to lowercase
        $slug = strtolower($name);
        
        // Replace spaces and special characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        
        // Remove leading/trailing hyphens
        $slug = trim($slug, '-');
        
        return $slug;
    }

    /**
     * Get store statistics
     *
     * @param string $storeId
     * @return array
     */
    public function getStoreStatistics(string $storeId): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        return $this->makeGetRequest("store/{$storeId}/statistics");
    }

    /**
     * Activate or deactivate a store
     *
     * @param string $storeId
     * @param bool $active
     * @return array
     */
    public function setStoreStatus(string $storeId, bool $active): array
    {
        if (empty($storeId)) {
            return [
                'success' => false,
                'message' => 'Store ID is required',
                'data' => null,
                'error_code' => 'MISSING_STORE_ID'
            ];
        }

        $payload = ['active' => $active];
        return $this->makePostRequest("store/{$storeId}/status", $payload);
    }
}
