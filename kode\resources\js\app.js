import './bootstrap';
import { createApp } from 'vue';
import PostScheduler from './components/post-scheduler.vue';

// Create Vue app
const app = createApp({});

// Register components
app.component('post-scheduler', PostScheduler);

// Mount Vue app to elements with data-vue attribute
document.addEventListener('DOMContentLoaded', function () {
    const vueElements = document.querySelectorAll('[data-vue]');
    vueElements.forEach(element => {
        app.mount(element);
    });
});
