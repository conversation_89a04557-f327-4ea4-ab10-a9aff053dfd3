<?php

namespace App\Http\Services\Account\whatsapp;

use App\Traits\AccountManager;
use App\Enums\AccountType;
use App\Enums\ConnectionType;
use App\Enums\PostStatus;
use App\Enums\PostType;
use App\Enums\StatusEnum;
use App\Models\Core\File;
use App\Models\MediaPlatform;
use App\Models\SocialAccount;
use App\Models\SocialPost;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class Account
{
    use AccountManager;

    const BASE_URL = 'https://waba-v2.360dialog.io';
    const VONAGE_BASE_URL = 'https://api.nexmo.com';
    const MEDIUM = 'whatsapp';

    /**
     * WhatsApp account connection
     *
     * @param MediaPlatform $platform
     * @param array $request
     * @param string $guard
     * @return array
     */
    public function whatsapp(MediaPlatform $platform, array $request, string $guard = 'admin'): array
    {
        $provider = Arr::get($request, 'provider', '360dialog');
        $apiKey = Arr::get($request, 'api_key');
        $phoneNumberId = Arr::get($request, 'phone_number_id');
        $businessAccountId = Arr::get($request, 'business_account_id');
        
        $response = response_status(translate('WhatsApp Account Created'));

        try {
            // Validate the connection based on provider
            if ($provider === '360dialog') {
                $validationResponse = $this->validate360DialogConnection($platform, $apiKey, $phoneNumberId);
            } else {
                $validationResponse = $this->validateVonageConnection($platform, $request);
            }

            if (!$validationResponse['status']) {
                return response_status($validationResponse['message'], 'error');
            }

            $accountInfo = [
                'id' => $phoneNumberId,
                'account_id' => $phoneNumberId,
                'name' => 'WhatsApp Business - ' . $phoneNumberId,
                'username' => $phoneNumberId,
                'avatar' => null,
                'link' => 'https://wa.me/' . $phoneNumberId,
                'account_type' => AccountType::PAGE->value,
                'connection_type' => ConnectionType::OAUTH->value,
                'token' => $apiKey,
                'provider' => $provider,
                'business_account_id' => $businessAccountId
            ];

            $response = $this->storeAccount(
                platform: $platform,
                accountInfo: $accountInfo,
                guard: $guard
            );

        } catch (\Exception $ex) {
            $response = response_status(strip_tags($ex->getMessage()), 'error');
        }

        return $response;
    }

    /**
     * Validate 360Dialog connection
     */
    private function validate360DialogConnection(MediaPlatform $platform, string $apiKey, string $phoneNumberId): array
    {
        try {
            $baseUrl = $platform->configuration->{'360dialog_base_url'} ?? self::BASE_URL;
            
            $response = Http::withHeaders([
                'D360-API-KEY' => $apiKey,
                'Content-Type' => 'application/json'
            ])->get($baseUrl . '/v1/configs/webhook');

            if ($response->successful()) {
                return ['status' => true, 'message' => 'Connection validated'];
            }

            return ['status' => false, 'message' => 'Invalid API key or configuration'];
        } catch (\Exception $e) {
            return ['status' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
        }
    }

    /**
     * Validate Vonage connection
     */
    private function validateVonageConnection(MediaPlatform $platform, array $request): array
    {
        try {
            $apiKey = Arr::get($request, 'vonage_api_key');
            $apiSecret = Arr::get($request, 'vonage_api_secret');
            
            $response = Http::withBasicAuth($apiKey, $apiSecret)
                ->get(self::VONAGE_BASE_URL . '/account/get-balance');

            if ($response->successful()) {
                return ['status' => true, 'message' => 'Connection validated'];
            }

            return ['status' => false, 'message' => 'Invalid Vonage credentials'];
        } catch (\Exception $e) {
            return ['status' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
        }
    }

    /**
     * Send WhatsApp Status post
     *
     * @param SocialPost $post
     * @return array
     */
    public function send(SocialPost $post): array
    {
        try {
            $account = $post->account;
            $accountConnection = $this->accountDetails($post->account);
            
            $isConnected = Arr::get($accountConnection, 'status', false);
            $message = translate("Gateway connection error");
            $status = false;
            $url = null;

            if ($isConnected && $post->post_type == PostType::WHATSAPP_STATUS->value) {
                $platform = $account->platform;
                $provider = $account->provider ?? '360dialog';
                
                if ($provider === '360dialog') {
                    $gwResponse = $this->send360DialogStatus($post, $platform, $account);
                } else {
                    $gwResponse = $this->sendVonageStatus($post, $platform, $account);
                }

                $status = Arr::get($gwResponse, 'status', false);
                $message = Arr::get($gwResponse, 'message', 'Status posted successfully');
                $url = Arr::get($gwResponse, 'url');
            }

        } catch (\Exception $ex) {
            $status = false;
            $message = strip_tags($ex->getMessage());
        }

        return [
            'status' => $status,
            'response' => $message,
            'url' => $url
        ];
    }

    /**
     * Send status via 360Dialog
     */
    private function send360DialogStatus(SocialPost $post, MediaPlatform $platform, SocialAccount $account): array
    {
        try {
            $baseUrl = $platform->configuration->{'360dialog_base_url'} ?? self::BASE_URL;
            $apiKey = $account->token;
            $phoneNumberId = $account->account_id;

            // Prepare status content
            $statusData = [
                'messaging_product' => 'whatsapp',
                'recipient_type' => 'individual',
                'to' => '<EMAIL>', // WhatsApp Status broadcast
                'type' => 'text'
            ];

            // Add text content
            if ($post->content) {
                $statusData['text'] = ['body' => $post->content];
            }

            // Add link if available
            if ($post->link) {
                $statusData['text']['body'] .= "\n\n" . $post->link;
            }

            // Handle media files
            if ($post->file && $post->file->count() > 0) {
                $file = $post->file->first();
                $mediaUrl = imageURL($file, "post", true);
                
                if (isValidVideoUrl($mediaUrl)) {
                    $statusData['type'] = 'video';
                    $statusData['video'] = [
                        'link' => $mediaUrl,
                        'caption' => $post->content
                    ];
                    unset($statusData['text']);
                } else {
                    $statusData['type'] = 'image';
                    $statusData['image'] = [
                        'link' => $mediaUrl,
                        'caption' => $post->content
                    ];
                    unset($statusData['text']);
                }
            }

            $response = Http::withHeaders([
                'D360-API-KEY' => $apiKey,
                'Content-Type' => 'application/json'
            ])->post($baseUrl . '/v1/messages', $statusData);

            $responseData = $response->json();

            if ($response->successful() && isset($responseData['messages'])) {
                return [
                    'status' => true,
                    'message' => 'WhatsApp Status posted successfully',
                    'url' => 'https://wa.me/' . $phoneNumberId
                ];
            }

            return [
                'status' => false,
                'message' => Arr::get($responseData, 'error.message', 'Failed to post WhatsApp Status')
            ];

        } catch (\Exception $e) {
            return [
                'status' => false,
                'message' => 'Error posting status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send status via Vonage
     */
    private function sendVonageStatus(SocialPost $post, MediaPlatform $platform, SocialAccount $account): array
    {
        try {
            // Vonage WhatsApp Status implementation
            // This would require Vonage's WhatsApp Business API setup
            
            return [
                'status' => false,
                'message' => 'Vonage WhatsApp Status posting not yet implemented'
            ];

        } catch (\Exception $e) {
            return [
                'status' => false,
                'message' => 'Error posting status via Vonage: ' . $e->getMessage()
            ];
        }
    }
}
