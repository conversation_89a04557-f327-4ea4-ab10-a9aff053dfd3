<?php

namespace App\Services\Nazmart;

/**
 * Nazmart Product URL Service
 * 
 * Handles all product URL generation, management, and validation operations
 * including URL generation, slug management, and URL validation.
 */
class ProductURLService extends BaseNazmartService
{
    /**
     * Generate product URL from product data
     *
     * @param array $productData
     * @return string
     */
    public function generateProductUrl(array $productData): string
    {
        $baseUrl = config('settings.social_platforms.nazmart.credential.base_url', 'https://vendora.com');
        
        // Try different URL generation strategies
        if (isset($productData['product_url'])) {
            return $productData['product_url'];
        }

        if (isset($productData['slug']) && isset($productData['store_slug'])) {
            return rtrim($baseUrl, '/') . '/stores/' . $productData['store_slug'] . '/products/' . $productData['slug'];
        }

        if (isset($productData['product_id']) && isset($productData['store_slug'])) {
            return rtrim($baseUrl, '/') . '/stores/' . $productData['store_slug'] . '/products/' . $productData['product_id'];
        }

        if (isset($productData['product_name']) && isset($productData['store_slug'])) {
            $slug = $this->generateSlugFromName($productData['product_name']);
            return rtrim($baseUrl, '/') . '/stores/' . $productData['store_slug'] . '/products/' . $slug;
        }

        // Fallback to store URL if product-specific URL cannot be generated
        if (isset($productData['store_slug'])) {
            return rtrim($baseUrl, '/') . '/stores/' . $productData['store_slug'];
        }

        return $baseUrl;
    }

    /**
     * Get product URL by product ID
     *
     * @param string $productId
     * @param string $storeSlug
     * @return array
     */
    public function getProductUrl(string $productId, string $storeSlug = null): array
    {
        if (empty($productId)) {
            return [
                'success' => false,
                'message' => 'Product ID is required',
                'data' => null,
                'error_code' => 'MISSING_PRODUCT_ID'
            ];
        }

        $params = ['product_id' => $productId];
        if ($storeSlug) {
            $params['store_slug'] = $storeSlug;
        }

        $result = $this->makeGetRequest('product/url', $params);

        if ($result['success'] && isset($result['data']['url'])) {
            return $result;
        }

        // Fallback URL generation if API doesn't provide URL
        $baseUrl = config('settings.social_platforms.nazmart.credential.base_url', 'https://vendora.com');
        $fallbackUrl = rtrim($baseUrl, '/') . '/products/' . $productId;

        if ($storeSlug) {
            $fallbackUrl = rtrim($baseUrl, '/') . '/stores/' . $storeSlug . '/products/' . $productId;
        }

        return [
            'success' => true,
            'message' => 'Product URL generated (fallback)',
            'data' => [
                'url' => $fallbackUrl,
                'is_fallback' => true
            ]
        ];
    }

    /**
     * Generate multiple product URLs
     *
     * @param array $productIds
     * @param string $storeSlug
     * @return array
     */
    public function getMultipleProductUrls(array $productIds, string $storeSlug = null): array
    {
        if (empty($productIds)) {
            return [
                'success' => false,
                'message' => 'Product IDs are required',
                'data' => [],
                'error_code' => 'MISSING_PRODUCT_IDS'
            ];
        }

        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($productIds as $productId) {
            $result = $this->getProductUrl($productId, $storeSlug);
            $results[$productId] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'message' => "Generated {$successCount} URLs successfully, {$failureCount} failed",
            'data' => [
                'urls' => $results,
                'summary' => [
                    'total' => count($productIds),
                    'success' => $successCount,
                    'failed' => $failureCount
                ]
            ]
        ];
    }

    /**
     * Validate product URL
     *
     * @param string $url
     * @return array
     */
    public function validateProductUrl(string $url): array
    {
        if (empty($url)) {
            return [
                'success' => false,
                'message' => 'URL is required',
                'data' => null,
                'error_code' => 'MISSING_URL'
            ];
        }

        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return [
                'success' => false,
                'message' => 'Invalid URL format',
                'data' => null,
                'error_code' => 'INVALID_URL_FORMAT'
            ];
        }

        // Check if URL belongs to Nazmart domain
        $baseUrl = config('settings.social_platforms.nazmart.credential.base_url', 'https://vendora.com');
        $baseDomain = parse_url($baseUrl, PHP_URL_HOST);
        $urlDomain = parse_url($url, PHP_URL_HOST);

        if ($urlDomain !== $baseDomain) {
            return [
                'success' => false,
                'message' => 'URL does not belong to Nazmart domain',
                'data' => [
                    'expected_domain' => $baseDomain,
                    'actual_domain' => $urlDomain
                ],
                'error_code' => 'INVALID_DOMAIN'
            ];
        }

        return [
            'success' => true,
            'message' => 'URL is valid',
            'data' => [
                'url' => $url,
                'domain' => $urlDomain,
                'is_valid' => true
            ]
        ];
    }

    /**
     * Generate URL-friendly slug from product name
     *
     * @param string $name
     * @return string
     */
    public function generateSlugFromName(string $name): string
    {
        // Convert to lowercase
        $slug = strtolower($name);
        
        // Replace spaces and special characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        
        // Remove leading/trailing hyphens
        $slug = trim($slug, '-');
        
        // Limit length
        $slug = substr($slug, 0, 100);
        
        return $slug;
    }

    /**
     * Extract product information from URL
     *
     * @param string $url
     * @return array
     */
    public function extractProductInfoFromUrl(string $url): array
    {
        $validation = $this->validateProductUrl($url);
        if (!$validation['success']) {
            return $validation;
        }

        $parsedUrl = parse_url($url);
        $pathParts = explode('/', trim($parsedUrl['path'], '/'));

        $productInfo = [
            'url' => $url,
            'path_parts' => $pathParts
        ];

        // Try to extract store slug and product identifier
        if (count($pathParts) >= 4 && $pathParts[0] === 'stores' && $pathParts[2] === 'products') {
            $productInfo['store_slug'] = $pathParts[1];
            $productInfo['product_identifier'] = $pathParts[3];
        } elseif (count($pathParts) >= 2 && $pathParts[0] === 'products') {
            $productInfo['product_identifier'] = $pathParts[1];
        }

        return [
            'success' => true,
            'message' => 'Product information extracted from URL',
            'data' => $productInfo
        ];
    }

    /**
     * Generate short URL for product
     *
     * @param string $productUrl
     * @return array
     */
    public function generateShortUrl(string $productUrl): array
    {
        $validation = $this->validateProductUrl($productUrl);
        if (!$validation['success']) {
            return $validation;
        }

        $payload = ['url' => $productUrl];
        return $this->makePostRequest('product/short-url', $payload);
    }

    /**
     * Get product URL with tracking parameters
     *
     * @param string $productUrl
     * @param array $trackingParams
     * @return string
     */
    public function addTrackingToUrl(string $productUrl, array $trackingParams = []): string
    {
        if (empty($trackingParams)) {
            return $productUrl;
        }

        $parsedUrl = parse_url($productUrl);
        $queryParams = [];

        // Parse existing query parameters
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
        }

        // Add tracking parameters
        $queryParams = array_merge($queryParams, $trackingParams);

        // Rebuild URL
        $url = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        
        if (isset($parsedUrl['port'])) {
            $url .= ':' . $parsedUrl['port'];
        }
        
        if (isset($parsedUrl['path'])) {
            $url .= $parsedUrl['path'];
        }
        
        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }
        
        if (isset($parsedUrl['fragment'])) {
            $url .= '#' . $parsedUrl['fragment'];
        }

        return $url;
    }

    /**
     * Check if product URL is accessible
     *
     * @param string $productUrl
     * @return array
     */
    public function checkUrlAccessibility(string $productUrl): array
    {
        $validation = $this->validateProductUrl($productUrl);
        if (!$validation['success']) {
            return $validation;
        }

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $productUrl);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            
            curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if (!empty($error)) {
                return [
                    'success' => false,
                    'message' => 'URL accessibility check failed: ' . $error,
                    'data' => ['url' => $productUrl, 'error' => $error],
                    'error_code' => 'CURL_ERROR'
                ];
            }

            $isAccessible = $httpCode >= 200 && $httpCode < 400;

            return [
                'success' => $isAccessible,
                'message' => $isAccessible ? 'URL is accessible' : 'URL is not accessible',
                'data' => [
                    'url' => $productUrl,
                    'http_code' => $httpCode,
                    'is_accessible' => $isAccessible
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'URL accessibility check failed: ' . $e->getMessage(),
                'data' => ['url' => $productUrl],
                'error_code' => 'EXCEPTION'
            ];
        }
    }
}
