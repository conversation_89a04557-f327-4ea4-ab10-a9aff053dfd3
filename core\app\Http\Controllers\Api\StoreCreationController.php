<?php

namespace App\Http\Controllers\Api;

use App\Actions\Tenant\TenantCreateEventWithMail;
use App\Actions\Tenant\TenantTrialPaymentLog;
use App\Http\Controllers\Controller;
use App\Models\PricePlan;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;

class StoreCreationController extends Controller
{
    /**
     * Create a new store/tenant via API
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function createStore(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:191',
                'email' => 'required|email|max:191|unique:users,email',
                'store_name' => 'required|string|max:191',
                'phone' => 'required|string|max:20',
                'category' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validated = $validator->validated();

            // Generate unique subdomain from store name
            $subdomain = $this->generateUniqueSubdomain($validated['store_name']);

            // Generate username from email
            $username = $this->generateUniqueUsername($validated['email']);

            // Create the user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'mobile' => $validated['phone'],
                'username' => $username,
                'subdomain' => $subdomain,
                'password' => Hash::make($this->generateRandomPassword()),
                'company' => $validated['store_name'],
                'country' => 'US', // Default country, can be made configurable
                'email_verified' => 1, // Auto-verify for API created stores
                'terms_condition' => 1
            ]);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create user'
                ], 500);
            }

            // Get default/trial price plan
            $defaultPlan = PricePlan::where('status', 1)
                ->where('type', 0) // Trial plan
                ->first();

            if (!$defaultPlan) {
                // If no trial plan, get the first active plan
                $defaultPlan = PricePlan::where('status', 1)->first();
            }

            if (!$defaultPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active price plan available'
                ], 500);
            }

            // Create tenant and setup
            $tenantCreated = TenantCreateEventWithMail::tenant_create_event_with_credential_mail($user, $subdomain);

            if ($tenantCreated) {
                // Create trial payment log
                TenantTrialPaymentLog::trial_payment_log(
                    $user,
                    $defaultPlan,
                    $subdomain,
                    get_static_option_central('default_theme') ?? 'casual'
                );

                // Get the created tenant
                $tenant = DB::table('tenants')->where('id', $subdomain)->first();

                return response()->json([
                    'success' => true,
                    'message' => 'Store created successfully',
                    'data' => [
                        'store_id' => $tenant->id,
                        'user_id' => $user->id,
                        'store_name' => $validated['store_name'],
                        'subdomain' => $subdomain,
                        'store_url' => $this->generateStoreUrl($subdomain),
                        'admin_email' => $user->email,
                        'admin_username' => get_static_option_central('tenant_admin_default_username') ?? 'super_admin',
                        'category' => $validated['category'] ?? null,
                        'plan' => [
                            'id' => $defaultPlan->id,
                            'name' => $defaultPlan->title,
                            'type' => $defaultPlan->type == 0 ? 'trial' : 'paid'
                        ]
                    ]
                ], 201);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to create tenant'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the store',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate unique subdomain from store name
     */
    private function generateUniqueSubdomain(string $storeName): string
    {
        $baseSubdomain = Str::slug($storeName);
        $subdomain = $baseSubdomain;
        $counter = 1;

        while (DB::table('tenants')->where('id', $subdomain)->exists() || User::where('subdomain', $subdomain)->exists()) {
            $subdomain = "{$baseSubdomain}-{$counter}";
            $counter++;
        }

        return $subdomain;
    }

    /**
     * Generate unique username from email
     */
    private function generateUniqueUsername(string $email): string
    {
        $baseUsername = Str::slug(explode('@', $email)[0]);
        $username = $baseUsername;
        $counter = 1;

        while (User::where('username', $username)->exists()) {
            $username = "{$baseUsername}-{$counter}";
            $counter++;
        }

        return $username;
    }

    /**
     * Generate random password
     */
    private function generateRandomPassword(): string
    {
        return get_static_option_central('tenant_admin_default_password') ?? Str::random(12);
    }

    /**
     * Generate store URL from subdomain
     */
    private function generateStoreUrl(string $subdomain): string
    {
        $centralDomain = config('tenancy.central_domains')[0] ?? 'localhost';
        return "https://{$subdomain}.{$centralDomain}";
    }

    /**
     * Get store information by store ID or subdomain
     */
    public function getStoreInfo(Request $request)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant by ID or subdomain
            $tenant = null;
            if (is_numeric($storeId)) {
                $tenant = Tenant::find($storeId);
            } else {
                // Try to find by subdomain
                $tenant = Tenant::where('id', 'like', $storeId . '%')->first();
            }

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $tenantData = json_decode($tenant->data, true);

            return response()->json([
                'success' => true,
                'data' => [
                    'store_id' => $tenant->id,
                    'subdomain' => $tenantData['tenancy_db_name'] ?? 'unknown',
                    'store_url' => 'http://' . ($tenantData['tenancy_db_name'] ?? 'unknown') . '.localhost',
                    'admin_url' => 'http://' . ($tenantData['tenancy_db_name'] ?? 'unknown') . '.localhost/admin',
                    'status' => 'active',
                    'created_at' => $tenant->created_at,
                    'updated_at' => $tenant->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve store information: ' . $e->getMessage()
            ], 500);
        }
    }
}
