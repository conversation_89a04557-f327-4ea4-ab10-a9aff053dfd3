import 'package:flutter/foundation.dart';
import '../../domain/entities/post.dart';

enum PostProviderStatus { initial, loading, loaded, creating, error }

class PostProvider extends ChangeNotifier {
  PostProviderStatus _status = PostProviderStatus.initial;
  List<Post> _posts = [];
  List<String> _selectedPlatforms = [];
  String _content = '';
  List<String> _mediaUrls = [];
  DateTime? _scheduledAt;
  String? _errorMessage;

  PostProviderStatus get status => _status;
  List<Post> get posts => _posts;
  List<String> get selectedPlatforms => _selectedPlatforms;
  String get content => _content;
  List<String> get mediaUrls => _mediaUrls;
  DateTime? get scheduledAt => _scheduledAt;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == PostProviderStatus.loading;
  bool get isCreating => _status == PostProviderStatus.creating;

  Future<void> loadPosts() async {
    _setStatus(PostProviderStatus.loading);

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1));

      // Mock data
      _posts = [
        Post(
          id: '1',
          userId: '1',
          content: 'Exciting news coming soon! 🎉',
          type: PostType.text,
          status: PostStatus.published,
          platforms: ['facebook', 'instagram'],
          media: [],
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        Post(
          id: '2',
          userId: '1',
          content: 'Check out our latest collection',
          type: PostType.image,
          status: PostStatus.scheduled,
          platforms: ['instagram', 'twitter'],
          media: [],
          scheduledAt: DateTime.now().add(const Duration(hours: 2)),
          createdAt: DateTime.now().subtract(const Duration(hours: 3)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 3)),
        ),
      ];

      _setStatus(PostProviderStatus.loaded);
    } catch (e) {
      _setError('Failed to load posts');
    }
  }

  Future<void> createPost() async {
    if (_content.isEmpty || _selectedPlatforms.isEmpty) {
      _setError('Please fill in all required fields');
      return;
    }

    _setStatus(PostProviderStatus.creating);

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2));

      final newPost = Post(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: '1',
        content: _content,
        type: _mediaUrls.isNotEmpty ? PostType.image : PostType.text,
        status: _scheduledAt != null ? PostStatus.scheduled : PostStatus.published,
        platforms: _selectedPlatforms,
        media: _mediaUrls.map((url) => PostMedia(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          url: url,
          type: 'image',
        )).toList(),
        scheduledAt: _scheduledAt,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _posts.insert(0, newPost);
      _clearForm();
      _setStatus(PostProviderStatus.loaded);
    } catch (e) {
      _setError('Failed to create post');
    }
  }

  void updateContent(String content) {
    _content = content;
    notifyListeners();
  }

  void togglePlatform(String platform) {
    if (_selectedPlatforms.contains(platform)) {
      _selectedPlatforms.remove(platform);
    } else {
      _selectedPlatforms.add(platform);
    }
    notifyListeners();
  }

  void addMediaUrl(String url) {
    _mediaUrls.add(url);
    notifyListeners();
  }

  void removeMediaUrl(String url) {
    _mediaUrls.remove(url);
    notifyListeners();
  }

  void setScheduledAt(DateTime? dateTime) {
    _scheduledAt = dateTime;
    notifyListeners();
  }

  void _clearForm() {
    _content = '';
    _selectedPlatforms.clear();
    _mediaUrls.clear();
    _scheduledAt = null;
  }

  void _setStatus(PostProviderStatus status) {
    _status = status;
    if (status != PostProviderStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(PostProviderStatus.error);
  }
}
