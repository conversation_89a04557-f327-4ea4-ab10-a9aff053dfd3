
.main {
  position: relative;
  z-index: 2;
}

@media (max-width: 991px) {
  .main {
    overflow-x: hidden;
  }
}

.fade-item {
  opacity: 0;
}
.bg--light {
  background-color: var(--color-white);
}

.section-fluid {
  padding: 0 3%;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .section-fluid {
    padding: 0 4%;
  }
}

@media (max-width: 767px) {
  .section-fluid {
    padding: 0 5%;
  }
}

.wrapper-fluid {
  overflow: hidden;
  max-width: 100%;
  position: relative;
}

@media (min-width: 1200px) {
  .wrapper-fluid {
    margin-left: calc((100% - 1320px) / 2);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .wrapper-fluid {
    margin-left: calc((100% - 1140px) / 2);
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .wrapper-fluid {
    margin-left: calc((100% - 935px) / 2);
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .wrapper-fluid {
    margin: 0 calc((100% - 700px) / 2);
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .wrapper-fluid {
    margin: 0 calc((100% - 540px) / 2);
  }
}


.preview-next {
  display: flex;
  align-items: center;
  border: 2px solid var(--color-primary-soft);
  border-radius: 50px;
}

.section-title-one .subtitle{
  display: inline-block;
  position: relative;
  line-height: 1;
  font-size: 18px;
  z-index: 1;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 5px;
}
.section-title-one .subtitle::after{
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 46px;
  height: 8px;
  background-color: var(--color-secondary);
  z-index: -1;
}
.section-title-one h2{
  font-size: 42px;
  margin-bottom: 10px;
}
@media (max-width: 768px){
  .section-title-one h2{
    font-size: 30px;
  }
}
.section-title-one h2 span{
  display: inline-block;
  color: var(--color-primary);
}


.section-title-two h2{
  font-size: 36px;
  margin-bottom: 10px;
}
@media (max-width: 768px){
  .section-title-two h2{
    font-size: 24px;
  }
}
.section-title-two h2 span{
  display: inline-block;
  color: var(--color-primary);
}

.form-control {
  padding: 15px 17px;
  font-size: 14px;
  color: var(--text-secondary);
  background-color: transparent;
  border: 1px solid var(--border-dark);
  border-radius: 4px;
}

.form-control:focus {
  background-color: transparent;
  border-color: var(--border-dark);
  box-shadow: none;
  color: var(--text-secondary);
}

.form__group {
  position: relative;
  max-width: 100%;
}

.form__group .form__field {
  font-family: inherit;
  width: 100%;
  border-bottom: 1px solid var(--border-two);
  outline: 0;
  font-size: 16px;
  color: var(--text-primary);
  padding: 10px 15px;
  background: rgba(240, 242, 248, 1);
  transition: border-color 0.2s;
}

.form__group .form__field::-webkit-input-placeholder {
  color: transparent;
}

.form__group .form__field::-moz-placeholder {
  color: transparent;
}

.form__group .form__field:-ms-input-placeholder {
  color: transparent;
}

.form__group .form__field::-ms-input-placeholder {
  color: transparent;
}

.form__group .form__field::placeholder {
  color: transparent;
}

.form__group .form__field:required,
.form__group .form__field:invalid {
  box-shadow: none;
}

.form__group .form__field:focus {
  border-color: var(--color-primary);
}

.form__group textarea.form__field {
  height: 80px;
}

.form__group .form__label {
  position: absolute;
  top: -25px;
  display: block;
  transition: 0.2s;
  font-size: 16px;
  color: var(--text-secondary);
  pointer-events: none;
}

.form__group .form__field:-moz-placeholder-shown ~ .form__label {
  cursor: text;
  top: 0;
}

.form__group .form__field:-ms-input-placeholder ~ .form__label {
  cursor: text;
  top: 0;
}

.form__group .form__field:placeholder-shown ~ .form__label {
  cursor: text;
  top: 11px;
  left: 18px;
}

.form__group .form__field:focus ~ .form__label {
  position: absolute;
  top: -25px;
  left: 0;
  display: block;
  transition: 0.2s;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
}

.average-rating {
  position: relative;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  color: transparent;
  display: inline-block;
  vertical-align: baseline;
  font-size: 25px;
  line-height: 1;
  height: 30px;
  width: 115px;
}

.average-rating::before {
  --percent: calc(4.5 / 5 * 100%);
  content: "★★★★★";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  color: rgba(0, 0, 0, 0.2);
  background: linear-gradient(
    90deg,
    rgb(255, 187, 0) var(--percent),
    rgb(199, 199, 199) var(--percent)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header {
  width: 100%;
  position: fixed;
  z-index: 99;
  top: 40px;
  left: 0;
  right: 0;
  padding: 0 45px 0 45px;
}

.header.sticky .header-shape-two{
  display: none;
  visibility: hidden;
}
 
@media (min-width: 992px) and (max-width: 1199px) {
  .header {
    padding: 0 4%;
  }
}

@media (max-width: 991px) {
  .header {
    padding: 10px 7%;
    top: 20px;
  }
}

@media (max-width: 768px) {
  .header {
    top: 10px;
  }
}

.header-left{
  background-color: var(--color-white);
  padding: 0px 30px;
  border-radius: 40px;
}
@media (max-width: 991px) {
  .header-left{
    padding: 0px 0px;
    overflow: hidden;
  }
}

.nav-right {
  padding: 0px;
  border-radius: 40px;
  position: relative;
  z-index: 1;
}

@media (max-width: 991px) {
  .header-shape-one{
    display: none;
    visibility: hidden;
  }

  .nav-right {
    background: #fff;
    padding: 5px 5px 5px 5px;
    margin-top: 0px;
    margin-right: 0px;
    border-radius: 40px;
    position: relative;
    z-index: 1;
  }
}

@media (max-width: 768px) {
  .nav-right {
    background: transparent;
  }
}

.header.sticky {
  background-color: rgba(255, 255, 255, 1);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  background-blend-mode: multiply;
  top: 0px;
  box-shadow: 0px 0px 10px rgba(0,0,0,0.15);
  animation: slowmove 0.45s linear;
}

@keyframes slowmove {
  0%{
    transform: translateY(-20px);
  }
  100%{
    transform: translateY(0px);
  }
}

.header.sticky .header-container .nav-right{
  padding: 0px;
} 


.header .header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}


@media (max-width: 768px) {
  .header .header-container {
    justify-content: center;
  }
}

.header-logo {
  min-width: 120px;
  max-width: 150px;
}

@media (max-width: 576px) {
  .header-logo {
    min-width: 120px;
    max-width: 150px;
  }
}

.header-logo img {
  width: 100%;
  height: 100%;
}

.sidebar .sidebar-body {
  background-color: var(--color-white);
  display: inline-block;
  padding: 0px 32px;
  border-radius: 40px;
}

.sidebar .sidebar-body .mobile-logo-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sidebar .sidebar-body .mobile-logo-area .mobile-menu-logo {
  display: none;
}

.sidebar .sidebar-body .menu-list {
  display: flex;
  align-items: center;
  gap: 35px;
}

.sidebar .sidebar-body .menu-list .menu-item {
  display: inline-block;
}

.sidebar .sidebar-body .menu-list .menu-item .menu-link {
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1;
  height: 70px;
  font-size: 17px;
  font-weight: 500;
}

.sidebar .sidebar-body .menu-list .menu-item .menu-link .menu-link-icon {
  font-size: 13px;
  height: 13px;
  vertical-align: middle;
  transition: 0.1s linear;
}

.sidebar .sidebar-body .menu-list .menu-item .menu-link:hover .menu-link-icon {
  transform: rotate(-180deg);
}

.sidebar .sidebar-body .menu-list .menu-item .menu-link:hover,
.sidebar .sidebar-body .menu-list .menu-item .menu-link.active {
  color: var(--color-primary);
}

.sidebar .sidebar-body .menu-list .menu-item .menu-link:hover + .mega-menu {
  transform: none;
  z-index: 200;
  transform: none;
  visibility: visible;
  opacity: 1;
  transform: perspective(400px) rotateX(0deg);
}

.mega-menu {
  width: 100%;
  position: absolute;
  left: 50%;
  top: calc(100% + 5px);
  transform: translateX(-50%) !important;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform-origin: 50% -50px;
  transform: perspective(400px) rotateX(-10deg);
  transition: all cubic-bezier(0.645, 0.045, 0.355, 1) 0.2s;
  transition-property: transform, opacity, visibility;
}

.mega-menu-wrapper {
  background-color: var(--color-white);
  border-radius: 8px;
  overflow: hidden;
  border-radius: 20px;
}

.mega-menu-wrapper .mega-menu-left {
  padding: 30px 0;
  position: relative;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .mega-menu-wrapper .mega-menu-left {
    padding: 20px 0;
  }
}

@media (max-width: 991px) {
  .mega-menu-wrapper .mega-menu-left {
    position: unset;
  }
}

.mega-menu-wrapper .mega-menu-left .maga-menu-item > h5 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-left: 30px;
}

@media (max-width: 991px) {
  .mega-menu-wrapper .mega-menu-left .maga-menu-item.menu-feature ul li {
    position: relative;
  }
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 17px 30px;
  border-radius: 4px;
  cursor: pointer;
}

@media (max-width: 991px) {
  .mega-menu-wrapper
    .mega-menu-left
    .maga-menu-item.menu-feature
    ul
    li
    > .menu-feature-item {
    padding: 10px 15px;
  }
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item:hover {
  background-color: var(--site-bg);
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item:hover
  div
  h6 {
  color: var(--color-primary);
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item
  > span {
  font-size: 24px;
  line-height: 1;
  color: var(--text-primary);
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item
  div
  p {
  font-size: 14px;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .menu-feature-item.hover
  + .sub-mega-menu {
  opacity: 1;
  visibility: visible;
  display: block;
}
.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item.hover {
  background-color: var(--site-bg);
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  > .menu-feature-item.hover
  div
  h6 {
  color: var(--color-primary);
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu {
  position: absolute;
  left: 100%;
  top: 0;
  width: 200%;
  height: 100%;
  background: var(--color-white);
  transition: 0.3s ease-in-out;
  opacity: 0;
  visibility: hidden;
  padding: 40px 30px;
  z-index: 1;
  box-shadow: rgba(100, 100, 111, 0.1) 0px 7px 20px 0px;
}

@media (max-width: 991px) {
  .mega-menu-wrapper
    .mega-menu-left
    .maga-menu-item.menu-feature
    ul
    li
    .sub-mega-menu {
    width: 100%;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    top: 100%;
    left: 0;
    padding: 20px 15px;
    position: relative;
    display: none;
    margin-bottom: 10px;
  }
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  inset: 0;
  z-index: -1;
  background-image: linear-gradient(
    160deg,
    #ffffff 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu
  .sub-menu-content
  > h5 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 700;
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu
  .sub-menu-content
  > ul {
  display: grid;
  gap: 10px;
  margin-top: 20px;
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu
  .sub-menu-content
  > ul
  li {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mega-menu-wrapper
  .mega-menu-left
  .maga-menu-item.menu-feature
  ul
  li
  .sub-mega-menu
  .sub-mega-menu-img {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
  isolation: isolate;
}

@media (max-width: 991px) {
  .mega-menu-wrapper
    .mega-menu-left
    .maga-menu-item.menu-feature
    ul
    li
    .sub-mega-menu
    .sub-mega-menu-img {
    opacity: 0.3;
  }
}

.mega-menu-wrapper .mega-menu-right {
  background: var(--site-bg);
  width: 100%;
  height: 100%;
}

.mega-menu-wrapper .mega-menu-right .social-integra {
  padding: 30px;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .mega-menu-wrapper .mega-menu-right .social-integra {
    padding: 20px;
  }
}

.mega-menu-wrapper .mega-menu-right .social-integra h5 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
}

.mega-menu-wrapper .mega-menu-right .social-integra .mega-menu-integra {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 15px;
}

.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: 20px;
    transition: 0.3s;
    border: 1px solid #eee;
    background-color: var(--color-white);
    @media (max-width:1399px) and (min-width:992px) {
      padding: 10px;
    }
}

.mega-menu-integra .nav-item{
  width: 100%;
}

@media (max-width: 768px){
  .mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item {
    gap: 15px;
    padding: 15px;
}

}

.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
}
.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item:hover .content h6{
  color: var(--color-white);
}
.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item:hover .content p{
  color: var(--color-white);
  opacity: 0.8;
}

.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item
  .social-item-img {
    min-width: 45px;
    max-width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #fff;
}
.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item
  .social-item-img  img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}

@media (max-width: 1199px) {
  .mega-menu-wrapper
    .mega-menu-right
    .social-integra
    .mega-menu-integra
    .menu-social-item
    .social-item-img {
    flex: 0 0 35px;
  }
}

.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item
  p {
  font-size: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.mega-menu-wrapper
  .mega-menu-right
  .social-integra
  .mega-menu-integra
  .menu-social-item
  span {
  font-size: 14px;
  color: var(--color-primary);
}

.menu-social-item.coming-soon {
  cursor: default;
}

.mega-menu-wrapper .mega-menu-right .mega-menu-banner {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.mega-menu-wrapper .mega-menu-right .mega-menu-banner img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.mega-menu:hover {
  visibility: visible;
  opacity: 1;
}

.nav-right .currency .custom--toggle {
  color: var(--text-secondary);
  opacity: 1;
  padding: 14px 15px;
  background: var(--color-primary-soft);
  border-radius: 60px;
  font-size: 14px;
}

.lang--toggle{
  width: 42px;
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.1;
  background: transparent;
  border-radius: 50%;
  color: var(--color-primary);
  background: var(--color-primary-soft);

}
.lang--toggle::after{
  display: none;
}

.lang .lang-btn {
  width: 42px;
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.1;
  background: transparent;
  border-radius: 50%;
  color: var(--color-primary);
  background: var(--color-primary-soft);
}

.lang .lang-btn.dropdown-toggle::after {
  display: none !important;
}

.lang .flag {
  flex: 0 0 30px;
  width: 30px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  overflow: hidden;
}

@media (max-width: 768px) {
}

.lang .flag img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.lang .dropdown-menu {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

.lang .dropdown-menu .dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1.1;
}

.lang .dropdown-menu .dropdown-item > i {
  font-size: 20px;
}

.profile-dropdown .profile-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  justify-content: center;
  gap: 10px;
  height: 42px;
  width: 42px;
  background-color: #f8f8f8;
  border-radius: 50px;
  color: var(--text-secondary);
}
.profile-dropdown .profile-btn::after {
  all: unset;
}
.profile-dropdown .profile-btn .profile-img {
  flex: 0 0 42px;
  width: 42px;
  aspect-ratio: 1/1;
}
@media (max-width: 576px) {
  .profile-dropdown .profile-btn .profile-img {
    flex: 0 0 25px;
    width: 25px;
  }
}
.profile-dropdown .profile-btn .profile-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 50%;
}
.profile-dropdown .profile-btn .balance {
  text-align: left;
}
.profile-dropdown .profile-btn .balance p {
  font-size: 12px;
  line-height: 1.1;
  margin-bottom: 3px;
  font-weight: 800;
}
.profile-dropdown .profile-btn .balance > h6 {
  font-weight: 700;
  font-size: 14px;
  color: var(--color-primary);
}
.profile-dropdown .dropdown-menu {
  min-width: 240px;
  max-width: 100%;
  padding-top: 0;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.profile-dropdown .dropdown-menu .dropdown-item {
  padding: 10px 15px;
  font-size: 14px !important;
  line-height: 1.1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
}
.dropdown .dropdown-menu .dropdown-item > i {
  font-size: 17px;
}
.profile-dropdown .dropdown-menu .dropdown-menu-title {
  padding: 15px;
  margin-bottom: 10px;
  border-bottom: 1px dashed var(--border-light);
}
.profile-dropdown .dropdown-menu .dropdown-menu-title h6 {
  font-weight: 700;
}
.profile-dropdown .dropdown-menu .dropdown-menu-title h6 .user-name {
  color: var(--color-primary);
}
.profile-dropdown .dropdown-menu .dropdown-menu-footer {
  border-top: 1px solid var(--color-primary-light);
  padding: 10px 15px;
  margin-top: 10px;
}
.profile-dropdown .dropdown-menu .dropdown-menu-footer:hover {
  background-color: var(--color-primary-light);
}
.profile-dropdown .dropdown-menu .dropdown-menu-footer a {
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  padding: 10px 15px;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: var(--text-primary);
  line-height: 1.1;
  gap: 10px;
}
.profile-dropdown .dropdown-menu .dropdown-menu-footer a i {
  font-size: 16px;
}
@media (max-width: 576px) {
  .header .header-container {
    gap: 15px;
  }

  .lang .flag {
    flex: 0 0 25px;
    width: 25px;
  }

  .nav-right .currency .dropdown-toggle {
    padding: 14px 10px;
  }

  .profile-dropdown .profile-btn .profile-img {
    flex: 0 0 30px;
    width: 30px;
  }

  .profile-dropdown .profile-btn .balance > h6 {
    font-size: 12px;
  }
}

.mobile-menu-btn {
  font-size: 22px;
  color: var(--color-white);
  line-height: 42px;
  width: 42px;
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-dark);
  border-radius: 50%;
  cursor: pointer;
}

@media (max-width:768px) {
  .mobile-menu-btn {
    line-height: 35px;
    width: 35px;
}
}

.nav-right .i-btn.btn--lg {
  padding: 18px 34px;
}

@media (max-width: 991px) {
  .header .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1200;
  }

  .header .sidebar.show-sidebar {
    height: 100vh;
    width: 100%;
  }

  .header .sidebar.show-sidebar .sidebar-body {
    transform: translateX(0);
  }

  .header .sidebar .sidebar-body {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 300px;
    padding: 20px !important;
    display: block;
    z-index: 1;
    min-height: 100dvh;
    height: 100%;
    overflow: hidden;
    background: var(--color-white);
    transform: translateX(-110%);
    transition: transform 0.3s;
    border-right: 1px solid var(--color-primary-light);
    border-radius: 0px;
  }

  .header .sidebar .sidebar-body .mobile-menu-logo {
    text-align: left;
    padding-top: 20px;
    display: block;
    padding-bottom: 8px;
  }

  .mobile-logo-wrap > a {
    display: inline-block;
    max-width: 150px;
  }

  .header .sidebar .sidebar-body .closer-sidebar {
    flex: 0 0 30px;
    width: 30px;
    aspect-ratio: 1/1;
    line-height: 1;
    border-radius: 50%;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-primary-light);
    color: var(--color-dark);
    cursor: pointer;
  }

  .header .sidebar .sidebar-body .closer-sidebar svg {
    width: 12px;
    height: 12px;
    fill: var(--text-primary);
  }

  .header .sidebar .sidebar-body .closer-sidebar:hover {
    background-color: var(--color-primary);
    color: var(--color-primary-text) !important;
  }

  .header .sidebar .sidebar-body .closer-sidebar:hover svg {
    fill: var(--color-primary-text);
  }

  .header .sidebar .sidebar-body nav {
    margin-bottom: 40px;
  }
  .header .sidebar .sidebar-body .sidebar-wrapper {
    max-height: calc(100svh - 110px);
    overflow-y: auto;
  }
  .header .sidebar .sidebar-body nav .menu-list {
    align-items: flex-start;
    flex-direction: column;
    gap: 15px;
  }

  .header .sidebar .sidebar-body nav .menu-list .menu-item {
    position: relative;
    width: 100%;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item:first-of-type
    .menu-link {
    padding-top: 0;
  }

  .header .sidebar .sidebar-body nav .menu-list .menu-item .menu-link {
    justify-content: space-between;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    padding: 10px 0;
  }

  .header .sidebar .sidebar-body nav .menu-list .menu-item .menu-link.active {
    color: var(--color-primary);
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .menu-link.active
    .menu-link-icon {
    transform: rotate(-180deg);
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .menu-link.active
    + .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    display: block;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .menu-link:not(.active)
    .menu-link-icon {
    transform: rotate(0deg);
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .menu-link:hover
    + .mega-menu {
    visibility: hidden;
    opacity: 0;
  }

  .header .sidebar .sidebar-body nav .menu-list .menu-item .mega-menu {
    position: relative;
    display: none;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .mega-menu
    .mega-menu-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .mega-menu
    .mega-menu-wrapper
    .mega-menu-left {
    padding: 15px 0;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .mega-menu
    .mega-menu-wrapper
    .mega-menu-left
    .maga-menu-item
    > h5 {
    padding-left: 15px;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .mega-menu
    .mega-menu-wrapper
    .social-integra {
    padding: 10px;
  }

  .header
    .sidebar
    .sidebar-body
    nav
    .menu-list
    .menu-item
    .mega-menu
    .mega-menu-wrapper
    .social-integra
    .mega-menu-integra {
    grid-template-columns: repeat(1, 1fr);
  }

  .header .sidebar .sidebar-body .sidebar-bottom {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    padding: 0 20px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header .sidebar .sidebar-overlay {
    position: absolute;
    background-color: var(--color-dark);
    opacity: 0.4;
    inset: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
  }
}

@media (max-width: 767px) {
  .container,
  .container-fluid {
    --bs-gutter-x: 2.5rem !important;
  }
}

.banner-section {
  position: relative;
  overflow: hidden;
  height: 100%;
  padding: 15px 15px 0 15px;
}
.banner-wrapper {
  padding: 110px 8%;
  background: linear-gradient(109deg, rgba(201, 240, 255, 1), rgb(198 129 231 / 44%));
  border-radius: 30px;
  @media (max-width: 767px){
    border-radius: 20px;
  }
}

@media (max-width: 991px) {
  .banner-wrapper{
    padding: 110px 6%;
  }
  .banner-section {
    padding: 20px 10px 0 10px;
  }
}

.banner-content {
  text-align: left;
}

.banner-content h1 {
  font-size: 85px;
  font-weight: 600;
  margin-bottom: 5px;
  line-height: 1.06;
  display: inline-block;

}
.banner-content h1 img{
  width: 80px;
  display: inline-block;
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .banner-content h1 {
    font-size: 60px;
  }
}
.banner-content p {
  margin-bottom: 30px;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content h1 {
    font-size: 48px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .banner-content h1 {
    font-size: 48px;
  }
}

@media (max-width: 767px) {
  .banner-content h1 {
    font-size: 40px;
  }
}

.banner-content > p {
  font-size: 18px;
}


 .circle-container {
  position: relative;
}

.circle-container .circleButton {
  width: 60px;
  aspect-ratio: 1/1;
  font-size: 36px;
  font-weight: 700;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.circle-container .circleButton::before {
  content: "";
  position: absolute;
  width: 60px;
  height: 60px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.85);
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
}

.circle-container .circleButton span {
  position: absolute;
  border: 1px solid var(--border-light);
  width: 50%;
  height: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  z-index: 3;
  width: 100%;
  height: 100%;
}
.circle-container .circleButton span > a {
  color: var(--color-primary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle-container .circleButton span > a i {
  line-height: 1;
}

.circle-container .circleButton .textcircle {
  width: 150px;
  aspect-ratio: 1/1;
  fill: var(--color-dark);
  -webkit-animation-name: rotateCircle;
  animation-name: rotateCircle;
  -webkit-animation-duration: 20s;
  animation-duration: 20s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.sponsors-area{
  position: absolute;
  left: 0px;
  bottom: -15px;
  background-color: var(--color-primary);
  width: 100%;
  max-width: 750px;
  margin-left: 0;
  margin-right: auto;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 35px;
  border: 15px solid var(--color-white);
  z-index: 1;

  @media (max-width: 991px) {
    height: auto;
    border: 15px solid var(--color-white);
    left: unset;
    margin: 0 auto;
    background-color: var(--color-white);
    border-radius: 25px;
  }

  @media (max-width: 767.98px) {
    margin-top: 25px;
    border: none;
    border-radius: 0;
    position: unset;
  }
}

.vector-right{
  position: absolute;
  right: -80px;
  bottom: -15px;
  z-index: -1;
   svg{
    fill: var(--color-white);
    width: 80px;
    height: 80px;
  }
  
  @media (max-width: 991px) {
      display: none;
  }
}

.vector-left{
  position: absolute;
  left: -15px;
  top: -80px;
  z-index: -1;
  svg{
    fill: var(--color-white);
    width: 80px;
    height: 80px;
  }

  @media (max-width: 991px) {
      display: none;
  }
}



.platform {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.platform-content-img{
  background-color: rgba(255, 255, 255, 0.25);
  padding: 10px;
  border-radius: 30px;
}
.platform-content-img img{
  border-radius: 20px;
  width: 100%;
  object-fit: cover;
}

.platform::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 55%;
  background-color: var(--color-primary);
  z-index: -1;
}

@media (max-width: 991px) {
  .platform::after {
    top: unset;
    width: 100%;
    bottom: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .platform::after {
    height: 35%;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .platform::after {
    height: 40%;
  }
}

@media (max-width: 576px) {
  .platform::after {
    height: 30%;
  }
}

.integration {
  position: relative;
  overflow: hidden;
}

@media (max-width: 767px) {
  .integration {
    padding-bottom: 0;
  }
}

.integration::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(var(--site-bg), rgba(255, 255, 255, 0));
  z-index: 1;
  width: 100%;
  height: 160px;
}

.integration::before {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(rgba(255, 255, 255, 0), var(--site-bg));
  z-index: 1;
  width: 100%;
  height: 160px;
}

@media (min-width: 768px) and (max-width: 991px) {
  .features .feature-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}

.features .feature-list .nav-link {
  padding: 0;
  width: 100%;
}
@media (min-width: 992px) {
  .features .feature-list .nav-link:last-child .feature-card-item {
    border-bottom: none;
  }
}

.feature-section {
  overflow: hidden;
}

.features .feature-list .nav-link .feature-card-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px 20px;
  transition: 0.3s ease-in-out;
  cursor: pointer;
  border-bottom: 1px solid var(--border-light);
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .features .feature-list .nav-link .feature-card-item {
    padding: 20px 15px;
  }
}

@media (max-width: 991px) {
  .features .feature-list .nav-link .feature-card-item {
    padding: 15px;
  }
}

.features .feature-list .nav-link .feature-card-item:hover {
  background-color: var(--site-bg);
  border-color: var(--color-primary);
}

.features .feature-list .nav-link .feature-card-item:hover h4 {
  color: var(--color-primary);
}

.features .feature-list .nav-link .feature-card-item:hover span {
  color: var(--color-primary);
}

.features .feature-list .nav-link .feature-card-item span {
  display: grid;
  place-items: center;
  font-size: 30px;
  color: var(--text-primary);
  transition: 0.3s ease-in-out;
}

.features .feature-list .nav-link .feature-card-item h4 {
  font-size: 20px;
  font-weight: 700;
  transition: 0.3s ease-in-out;
}

@media (max-width: 767px) {
  .features .feature-list .nav-link .feature-card-item h4 {
    font-size: 17px;
  }
}

.features .feature-list .nav-link .feature-card-item p {
  margin-top: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.features .feature-list .nav-link.active .feature-card-item {
  background-color: var(--site-bg);
  border-color: var(--color-primary);
}

.features .feature-list .nav-link.active .feature-card-item h4 {
  color: var(--color-primary);
}

.features .feature-list .nav-link.active .feature-card-item span {
  color: var(--color-primary);
}

.feature-section{
  overflow: hidden;
}

.choose-card-wrapper .col-lg-4:nth-child(even) .choose-card{
  background-color: var(--color-primary);

  &:hover{
    padding: 25px;
    border-radius: 12px;
    border: 2px solid var(--color-primary);
    background-color: var(--color-white);
    transition: 0.3s ease-in-out;
    text-align: center;

    .choose-card-icon {
      color: var(--color-white);
      background-color: var(--color-primary);;
    }
    h4{
      color: var(--text-primary);
    }
    p{
      color: var(--text-secondary);
    }
  }

  h4 {
    font-size: 22px;
    font-weight: 700;
    color: var(--color-white);
  }
  
   p {
    color: var(--color-white);
  }
  .choose-card-icon {
    color: var(--color-primary);
    background-color: var(--color-white);
  }
}
.choose {
  position: relative;
}

.choose-card {
  padding: 25px;
  border-radius: 12px;
  border: 2px solid var(--color-primary);
  background-color: var(--color-white);
  transition: 0.3s ease-in-out;
  text-align: center;

  &:hover{
    background-color: var(--color-primary-light) !important;
  }
}

@media (max-width: 991px) {
  .choose-card {
    padding: 20px;
  }
}

.choose-card:hover {
  background-color: var(--color-white);
  border: 2px solid var(--color-primary);
}

.choose-card .choose-card-icon {
  margin: auto;
  font-size: 36px;
  margin-bottom: 25px;
  color: var(--color-white);
  background-color: var(--color-primary);
  display: inline-block;
  width: 70px;
  height: 70px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.choose-card h4 {
  font-size: 22px;
  font-weight: 700;
}

@media (max-width: 767px) {
  .choose-card h4 {
    font-size: 17px;
  }
}

.choose-card p {
  margin-top: 8px;
}

.plan-tab {
  display: flex;
  align-items: center;
  padding: 5px;
  background: var(--color-primary-light);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 50px;
  overflow: hidden;
}

.plan-tab.glider-tab{

}



.plan-tab.glider-tab button.active {
  background: var(--color-primary);
  color: var(--color-primary-text);
}

.plan-tab > button {
  padding: 11px 30px;
  border-radius: 50px;
  background: transparent;
  line-height: 1;
  color: var(--text-primary);
  font-size: 16px;
}

@media (max-width: 424.98px) {
  .plan-tab > button {
    padding: 10px 18px;
    font-size: 15px;
  }
}

.plan-tab > button:hover {
  color: var(--text-primary);
  text-decoration: underline;
}

.plan-tab > button.active {
  background: var(--color-primary);
  color: var(--color-primary-text);
}

.plan-tab > li a {
  padding: 14px 30px;
  border-radius: 50px;
  background: transparent;
  line-height: 1;
  color: var(--text-primary);
  font-size: 16px;
}

@media (max-width: 424.98px) {
  .plan-tab > li a {
    padding: 10px 18px;
    font-size: 15px;
  }
}

.plan-tab > li a:hover {
  color: var(--text-primary);
  text-decoration: underline;
}


.blog-style-one{
  position: relative;
}
.blog-style-one .image{
  border-radius: 30px;
  overflow: hidden;
}
.blog-style-one .content > span{
  display: inline-block;
  font-size: 18px;
  margin-bottom: 5px;
}
.blog-style-one .content h3{
  font-size: 30px;
  margin-bottom: 15px;
}
.blog-style-one .content h3 a{
  color: inherit;
}
.blog-style-one .content p{
  margin-bottom: 35px;
  font-size: 16px;
}

.blog-style-two{
  position: relative;
  margin-bottom: 15px;
}
.blog-style-two:hover .content{
  background-color: rgba(0, 0, 0, 1);
}
.blog-style-two .i-btn{
  position: absolute;
  left: -15px;
  bottom: -15px;
}
.blog-style-two .content{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  padding: 20px 25px 55px 25px;
  border-radius: 0px 0px 30px 30px;
  transition: 0.4s ease;
}
.blog-style-two .date{
  position: absolute;
  top: 20px;
  right: 25px;
  color: var(--color-white);
  font-size: 18px;
}
.blog-style-two .content span{
  display: inline-block;
  color: var(--color-white);
  opacity: 0.8;
  margin-bottom: 10px;
  font-size: 18px;
}
.blog-style-two .content h4{
  color: var(--color-white);
  font-size: 24px;
  font-weight: 500;
  transition: 0.3s ease;
}
.blog-style-two .content h4:hover{
  color: var(--color-primary);
}
.blog-style-two .content h4 a{
  color: inherit;
}
.blog-style-two .image{
  border-radius: 30px;
  overflow: hidden;
}

.blog-style-two .image img{
  height: 100% !important;
  width: 100%;
  min-height: 300px;
  object-fit: cover;
}

@media (min-width:768px) and (max-width:991px) {
  .blog-style-two .content h4{
    font-size: 22px;
  }
}

@media (max-width:767px) {
  .blog-style-two .content{
    padding: 15px 20px 50px 20px;
  }
  .blog-style-two .content h4{
    font-size: 20px;
  }
  .blog-style-two .content span{
    font-size: 14px;
  }
  .blog-style-two .date{
    font-size: 15px;
  }
  .blog-style-one .content p{
    margin-bottom: 15px;
  }
  .blog-style-one .content span{
    font-size: 14px;
  }
  .blog-style-one .content h3{
    font-size: 20px;
    margin-bottom: 10px;
  }
}

.cta {
  position: relative;
  isolation: isolate;
  overflow: hidden;
  height: 100%;
}

.cta::before {
  position: absolute;
  content: "";
  inset: 0;
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  z-index: -2;
  opacity: 0.15;
}

.cta::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 350px;
  background: linear-gradient(rgba(255, 255, 255, 0), #000);
  z-index: -1;
}

@media (max-width: 991px) {
  .cta::after {
    height: 100%;
  }
}


.ai {
  overflow: hidden;
}

.ai .horizontal-scroll .i-panel {
  width: 100%;
  height: 100%;
  position: relative;
}

.reviews .review-wrapper .swiper .swiper-pagination-bullet {
  width: var(
    --swiper-pagination-bullet-width,
    var(--swiper-pagination-bullet-size, 30px)
  );
  height: var(
    --swiper-pagination-bullet-height,
    var(--swiper-pagination-bullet-size, 6px)
  );
  border-radius: 20px;
  background: var(--color-primary-soft);
  opacity: 1;
}

.reviews
  .review-wrapper
  .swiper
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--color-primary);
}

.review-card:not(.review-title) {
  padding: 0px;
  position: relative;
  background-color: var(--text-primary);
  border-radius: 30px;
}
.review-card:not(.review-title) .quote-icon{
  position: absolute;
  right: 20px;
  top: 20px;
  transform: rotate(180deg);
}

.review-wrapper{
  position: relative;
  background-color: var(--text-primary);
  border-radius: 35px;
}

.review-content{
  padding: 30px 35px;
  min-height: 330px;
  flex-grow: 1;
  border-radius: 0 30px 0 0;
}
@media (max-width: 768px) {
  .review-content{
    border-radius: 0 0px 0 30px;
    padding: 25px 25px;
  }
}
.review-content p{
  color: var(--color-white);
  margin-top: 20px;
}
.review-image{
  min-width: 120px;
  height: 120px;
  border-radius: 20px 0 20px 0;
  overflow: hidden;
  border: 3px solid #fff;
  margin-top: 10px;
  margin-left: 10px;
}
@media (max-width: 768px) {
  .review-image{
    border-radius: 30px 0 30px 0;
  }
}
.review-image img{
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.review-card:not(.review-title) .quote-icon.quote-two {
  right: 20px;
  bottom: 20px;
  position: absolute;
}

.review-card:not(.review-title) .quote-icon i {
  font-size: 70px;
  color: var(--text-primary);
  opacity: 0.06;
}

.review-card:not(.review-title) p {
  font-size: 18px;
}
@media (max-width: 991px) {
  .review-card:not(.review-title) p {
    font-size: 16px;
  }
}

.review-card:not(.review-title) .reviewer-meta {
  display: flex;
  flex-direction: column;
  margin-top: 35px;
  gap: 5px;
}

.review-card:not(.review-title) .reviewer-meta .reviewer-img {
  flex: 0 0 60px;
  width: 60px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  overflow: hidden;
}

.review-card:not(.review-title) .reviewer-meta .reviewer-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.review-card:not(.review-title) .reviewer-meta h6 {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-white);
  display: block;
}

.review-card:not(.review-title) .reviewer-meta span {
  font-size: 14px;
  display: block;
  color: var(--color-white);
}

.review-rating {
  line-height: 1.4;
}

.review-rating li {
  font-size: 18px;
  color: #fcd93a;
}
.review-arrow-wrapper{
  display: flex;
  justify-content: center;
  background-color: var(--color-white);
  padding: 10px 10px;
  position: absolute;
  bottom: 0px;
  right: 0;
  z-index: 9;
  border-radius: 30px 0 0 0;
  gap: 10px;
}
.review-button-next{
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-white);
  text-align: center;
}
.review-button-prev{
  width: 40px;
  line-height: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--color-white);
  text-align: center;
}

.shape-radius-one{
  position: absolute;
  right: -23px;
  bottom: 37px;
  width: 60px;
  height: 60px;
  z-index: 2;
}
.shape-radius-two{
  width: 60px;
  height: 60px;
  position: absolute;
  right: 86px;
  bottom: -23px;
  z-index: 2;
}

footer{
  position: relative;
  z-index: 1;
  background-color: var(--text-primary);
  overflow: hidden;
}

.footer-top{
  position: relative;
}
.footer-top-img{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.footer-top-content{
  text-align: center;
  position: relative;
  z-index: 1;
}
.footer-top-content h2{
  color: var(--color-white);
  font-weight: 500;
  margin-bottom: 20px;
}
.footer-top-content p{
  color: var(--color-white);
  opacity: 0.55;
  width: 100%;
  max-width: 60%;
  margin: 0 auto;
  line-height: 1.4;
  margin-bottom: 40px;
}
.footer-title{
  color: var(--color-white);
  font-weight: 500;
  margin-bottom: 20px;
  font-size: 20px;
}
@media (max-width: 768px) {
  .footer-title{
    font-size: 18px;
    margin-bottom: 15px;
  }
  .footer-top-content p{
    max-width: 90%;
  }
}
.footer-list li{
  margin-bottom: 12px;
  position: relative;
  padding-left: 20px;
  transition: 0.4s ease;
}
.footer-list li:hover{
  transform: translateX(6px);
}
.footer-list li::before{
  content: '\F285';
  font-family: 'bootstrap-icons';
  position: absolute;
  left: 0;
  top: 3px;
  display: inline-block;
  color: var(--color-secondary);
  font-size: 12px;
  vertical-align: middle;
  transition: 0.4s ease;
}
.footer-list li:hover::before{
  color: var(--color-white);
}
.footer-list li:last-child{
  margin-bottom: 0px;
}
.footer-list li a{
  color: var(--color-white);
  opacity: 0.8;
  font-size: 16px;
}
@media (max-width: 768px) {
  .footer-list li a{
    font-size: 14px;
  }
}
.footer-list li a i{
  vertical-align: middle;
}
.footer-list span{
  display: block;
  color: var(--color-white);
  opacity: 0.5;
  font-size: 12px;
}
.footer-social ul{
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.footer-social ul li a{
  display: inline-flex;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: var(--color-secondary);
  color: var(--text-primary);
  font-size: 15px;
  justify-content: center;
  align-items: center;
  transition: 0.4s ease;
}
.footer-social ul li a:hover{
  background-color: var(--color-primary);
  color: var(--color-white);
}
.footer-social ul li a i{
  line-height: 1;
  vertical-align: middle;
}

.newsletter-wrapper{
  width: 100%;
  max-width: 800px;
  margin-left: auto;
  margin-right: 0;
  border-radius: 45px 0 0 45px;
  background-color: rgba(234, 236, 240, 1);
  padding: 10px;
  position: relative;
  z-index: 9;
}

  @media (max-width: 991px) {
    .newsletter-wrapper{
         border-radius: 45px;
    }
    .newsletter-wrapper::before{
        display: none !important;
    } 

     .newsletter-wrapper::after{
        display: none !important;
    } 
  }
.newsletter-wrapper::before{
  content: '';
  position: absolute;
  right: 0;
  top: -25px;
  width: 30px;
  height: 30px;
  background-color: rgba(234, 236, 240, 1);
  display: block;
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
}
.newsletter-wrapper::after{
  content: '';
  position: absolute;
  right: 0;
  bottom: -25px;
  width: 30px;
  height: 30px;
  background-color: rgba(234, 236, 240, 1);
  display: block;
  clip-path: polygon(100% 0, 0 0, 100% 100%);
}
.newsletter-wrapper form{
  display: flex;
  justify-content: flex-start;
  border: 1px solid var(--border-two);
  border-radius: 35px;
  padding: 10px;
  background-color: var(--color-white);
}
.newsletter-wrapper form input{
  background-color: var(--color-white);
  flex-grow: 1;
  border: none;
  padding: 10px 20px;
}

@media (max-width: 768px) {
  .newsletter-wrapper{
    padding: 10px;
  }
  .newsletter-wrapper form{
    flex-wrap: wrap;
    gap: 10px;
  }
  .newsletter-wrapper form button{
    width: 100%;
  }
}

.footer-bottom{
  padding: 60px 0px;
}
.copyright-area{
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding: 20px 0px;
}

.lang-modal {
  position: relative;
}

.lang-modal > img {
  opacity: 0.4;
}

.currency {
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1;
  background: #f8f8f8;
  border-radius: 30px;
}

.language.dropdown-toggle::after{
  content: unset;
}

 .custom--toggle {
  color: var(--text-light);
  text-transform: capitalize;
  font-size: 15px;
  transition: 0.3s ease;
  background: transparent;
}

 .custom--toggle:hover {
  text-decoration: underline;
}

 .dropdown-menu {
  border-color: var(--border-one);
  padding: 0 !important;
}

 .dropdown-menu .dropdown-item {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1.1;
  padding: 10px 15px;
}

 .dropdown-menu .dropdown-item:active {
  color: var(--color-primary-text);
  background-color: var(--color-primary);
}

 .dropdown-menu .dropdown-item:hover,
 .dropdown-menu .dropdown-item:focus {
  color: var(--color-white);
  background-color: var(--color-primary);
}

.dropdown-toggle.single-item::after {
  display: none !important;
}


.cookie {
  display: flex;
  align-items: center;
  gap: 40px;
  width: 60%;
  margin: 0 auto;
  padding: 25px;
  position: fixed;
  isolation: isolate;
  z-index: 200;
  right: 30px;
  bottom: 20px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  flex-direction: column;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1199px) {
  .cookie {
    width: 90%;
  }
}

@media (max-width: 991px) {
  .cookie {
    width: 95%;
    flex-direction: column;
    align-items: flex-end;
    gap: 25px;
    padding: 20px;
  }
}

.cookie .cookie-content {
  display: flex;
  align-items: self-start;
  gap: 20px;
}

.cookie .cookie-content .cookie-icon {
  flex: 0 0 50px;
  width: 50px;
}

@media (max-width: 576px) {
  .cookie .cookie-content .cookie-icon {
    flex: 0 0 35px;
    width: 35px;
  }
}

.cookie .cookie-content .cookie-icon svg {
  width: 100%;
  fill: var(--text-primary);
}

.cookie .cookie-action {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: flex-start;
  width: 100%;
}

@media (max-width: 576px) {
  .cookie .cookie-action {
    flex-wrap: wrap;
    width: 100%;
  }
}

@media screen and (max-width: 424px) {
  .cookie .cookie-action > button {
    width: 100%;
  }
}

.inner-banner {
  position: relative;
  overflow: hidden;
  padding: 20px 20px 0 20px;
}
.inner-banner-wrapper{
  padding: 180px 0 180px;
  background: linear-gradient(90deg,rgb(126, 212, 246), rgba(216, 137, 255, 0.94));
  border-radius: 30px;
  height: 100%;
  position: relative;
  z-index: 1;
  @media (max-width:991.98px) {
    border-radius: 25px;
  }
  @media (max-width:767.98px) {
    border-radius: 20px;
  }
}
.inner-banner-img{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 80%;
  opacity: 0.5;
}

@media (max-width: 767px) {
  .inner-banner {
    padding: 15px;
  }
  .inner-banner-wrapper{
    padding: 120px 0 120px;
  }
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content {
    text-align: center;
  }
}

.inner-banner .inner-banner-content > h2 {
  font-size: 52px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--color-white);
}

@media (min-width: 768px) and (max-width: 991px) {
  .inner-banner .inner-banner-content > h2 {
    font-size: 48px;
  }
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content > h2 {
    font-size: 36px;
  }
}

.inner-banner .inner-banner-content > p {
  font-size: 18px;
  color: var(--color-white);
  word-break: break-word;
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content > p {
    font-size: 15px;
  }
}

.inner-banner .inner-banner-content .blog-search {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 8px solid var(--color-primary-light);
  background: var(--color-white);
  border-radius: 60px;
  padding: 6px;
  margin-top: 40px;
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content .blog-search {
    flex-direction: column;
    align-items: flex-start;
    border-radius: 12px;
  }
}

.inner-banner .inner-banner-content .blog-search > input {
  border: none;
  width: 100%;
  background: transparent;
  border: none;
  color: var(--text-primary);
  padding: 0 15px;
  height: 42px;
  line-height: 42px;
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content .blog-search > input {
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 10px;
    height: 48px;
    line-height: 48px;
  }
}

.inner-banner .inner-banner-content .blog-search > div {
  display: flex;
  align-items: center;
  gap: 20px;
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content .blog-search > div {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .inner-banner .inner-banner-content .blog-search > div {
    gap: 15px;
  }
}

.inner-banner .inner-banner-content .blog-search > div .blog-filter {
  display: flex;
  align-items: center;
  gap: 30px;
  min-width: 180px;
  max-width: 100%;
  width: 100%;
}

@media (max-width: 767px) {
  .inner-banner .inner-banner-content .blog-search > div .blog-filter {
    width: 100%;
  }
}

.inner-banner
  .inner-banner-content
  .blog-search
  > div
  .blog-filter
  .filter-select {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
}

@media (max-width: 767px) {
  .inner-banner
    .inner-banner-content
    .blog-search
    > div
    .blog-filter
    .filter-select {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .inner-banner
    .inner-banner-content
    .blog-search
    > div
    .blog-filter
    .filter-select {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

.inner-banner
  .inner-banner-content
  .blog-search
  > div
  .blog-filter
  .filter-select
  .form-select {
  cursor: pointer;
  height: 48px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  line-height: 1.1;
  border: none;
  background-color: var(--site-bg);
  border-radius: 50px;
  padding: 0.375rem 2.25rem 0.375rem 1rem;
}

.inner-banner
  .inner-banner-content
  .blog-search
  > div
  .blog-filter
  .filter-select
  .form-select:focus {
  border-color: unset;
  outline: 0;
  box-shadow: none;
}

@media (max-width: 767px) {
  .inner-banner
    .inner-banner-content
    .blog-search
    > div
    .blog-filter
    .filter-select
    .form-select {
    width: 100%;
  }
}

.inner-banner .inner-banner-content .blog-search > div > button:hover {
  background-color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.inner-banner .inner-banner-content .blog-search > div > button:hover > svg {
  fill: var(--color-secondary-text);
}

.inner-banner .inner-banner-content .blog-search > div > button > svg {
  width: 20px;
  height: 20px;
  fill: var(--color-primary-text);
  transition: 0.4s ease-in-out;
}

@media (max-width: 576px) {
  .inner-banner .inner-banner-content .blog-search > div > button > svg {
    width: 18px;
    height: 18px;
  }
}

.inner-banner .inner-banner-content .blog-search > div > button.i-btn.btn--lg {
  padding: 15px;
}

@media (max-width: 576px) {
  .inner-banner
    .inner-banner-content
    .blog-search
    > div
    > button.i-btn.btn--lg {
    padding: 15px;
  }
}


.blog-d-image{
  overflow: hidden;
  border-radius: 30px;
}
.blog-details .title{
  font-size: 38px;
  margin-bottom: 20px;
}
.blog-details .sub-title{
  font-size: 24px;
}
.date li{
  font-size: 15px;
}
.blog-details .date{
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
}

.blog-details p{
  font-size: 16px;
}

.blog-category{
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  padding: 2px 10px;
  border-radius: 6px;
  display: inline-block;
}

.popular-post-list li{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  align-items: center;
  padding-bottom: 18px;
  margin-bottom: 18px;
  border-bottom: 1px solid var(--border-two);
}
.popular-post-list li:last-child{
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: unset;
}
.popular-post-list li .image{
  width: 90px;
  overflow: hidden;
  border-radius: 12px;
}
.popular-post-list li .content span{
  display: inline-block;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 400;
}
.popular-post-list li .content h6{
  font-size: 16px;
  margin-bottom: 0px;
}
.popular-post-list li .content h6 a{
  color: inherit;
}
.popular-post-list li .content > a{
  color: var(--color-primary);
}

.share-blog{
  display: flex;
  padding: 20px;
  border: 1px solid var(--border-two);
  border-radius: 40px;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 25px;
}

@media (max-width: 768px) {
  .blog-details .title {
    font-size: 26px;
    line-height: 1.25;
  }
  .date li{
    font-size: 14px;
  }
  .blog-details .sub-title{
    font-size: 18px;
  }
  .blog-details p{
    font-size: 15px;
  }
  .popular-post-list li .content h6 {
    font-size: 15px;
  }
  .popular-post-list li .content span {
    font-size: 11px;
  }
}

.blog-details .blog-detail-top {
  margin-bottom: 50px;
}

@media (max-width: 767px) {
  .blog-details .blog-detail-top {
    margin-bottom: 40px;
  }
}

.blog-details .blog-detail-top > h2 {
  font-size: 48px;
  font-weight: 700;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .blog-details .blog-detail-top > h2 {
    font-size: 36px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .blog-details .blog-detail-top > h2 {
    font-size: 36px;
  }
}

@media (max-width: 767px) {
  .blog-details .blog-detail-top > h2 {
    font-size: 28px;
  }
}

.blog-details .blog-thumbnail {
  border-radius: 10px;
  overflow: hidden;
}

.blog-details .blog-thumbnail img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.blog-details .blog-contents {
  margin-top: 50px;
}

@media (max-width: 767px) {
  .blog-details .blog-contents {
    margin-top: 30px;
    padding: 0;
  }
}

.blog-details .blog-contents .blog-share {
  border-top: 1px solid var(--border-light);
  padding-top: 22px;
  margin-top: 20px;
}

.blog-details .blog-contents .blog-share h6 {
  font-size: 20px;
  font-weight: 700;
}

.blog-details .blog-contents .blog-share .social-media {
  margin: 0;
}

.blog-details .blog-contents .blog-share .social-media a {
  color: var(--text-primary);
}

.blog-details .blog-contents .blog-share .social-media a:hover {
  color: var(--color-primary);
}

.blog-details .blog-newsletter {
  background: var(--color-white);
  padding: 25px;
  border: 1px solid var(--color-primary-light);
  margin-bottom: 40px;
}

@media (max-width: 587px) {
  .blog-details .blog-newsletter {
    padding: 20px;
  }
}

.blog-details .blog-newsletter h4 {
  margin-bottom: 15px;
}

.blog-details .blog-newsletter form {
  margin-top: 30px;
}

.blog-details .blog-newsletter form input {
  width: 100%;
  padding: 0 50px 0 20px;
  height: 55px;
  border: 1px solid var(--border-light);
  border-radius: 60px;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 35px;
  background: transparent;
  transition: all linear 0.3s;
  margin-bottom: 25px;
}

.blog-details .blog-newsletter form input:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--color-primary-light);
}

.blog-details .blog-newsletter form .i-btn.btn--lg {
  padding: 18.5px 30px;
}

.blog-details .resources .resource-list {
  display: grid;
  gap: 20px;
  margin-top: 20px;
}

.blog-details .resources .resource-list .resource-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.blog-details
  .resources
  .resource-list
  .resource-item:hover
  > .resource-content
  h5 {
  color: var(--color-primary);
  text-decoration: underline;
}

.blog-details .resources .resource-list .resource-item .resource-thumbnail {
  flex: 0 0 130px;
  width: 100%;
  height: auto;
  border-radius: 4px;
  overflow: hidden;
}

.blog-details .resources .resource-list .resource-item .resource-content h5 {
  font-size: 16px;
  font-weight: 700;
  transition: 0.3s;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.blog-details
  .resources
  .resource-list
  .resource-item
  .resource-content
  .blog-meta
  span:last-of-type::before {
  display: none;
}

.blog-section{
  overflow: hidden;
}

.contact-wrapper{
  border-radius: 40px;
  padding: 45px;
}

@media (min-width: 768px) and (max-width: 1199px) {
  .contact-wrapper{
    padding: 30px;
  }
}

.contact-form-wrapper{
  background-color: var(--color-white);
  border-radius: 30px;
  padding: 40px;
}

.contact {
  position: relative;
}


@media (max-width: 767px) {
  .contact-wrapper{
    padding: 15px;
  }
  .contact-form-wrapper{
    padding: 25px 20px;
  }
}

.contact .contact-left .contact-list {
  display: grid;
  gap: 30px;
}

.contact .contact-left .contact-list li {
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact .contact-left .contact-list li span {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  color: var(--color-white);
  background-color: var(--color-primary);
  border-radius: 12px;
  text-align: center;
}

.contact .contact-left .contact-list li a {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 16px;
}

.contact .contact-left .contact-list li a:hover {
  text-decoration: underline;
  opacity: 1;
}

.contact .contact-left .contact-time {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 30px;
}

.contact .contact-left .contact-time span {
  font-size: 17px;
  color: var(--color-white);
}

.contact .contact-left .contact-time p {
  font-size: 14px;
  color: var(--color-white);
  opacity: 0.9;
}

.contact .contact-form h4 {
  font-size: 40px;
  font-weight: 600;
  line-height: 1;
}

@media (min-width: 768px) and (max-width: 991px) {
  .contact .contact-form h4 {
    font-size: 34px;
  }
}

@media (max-width: 767px) {
  .contact .contact-form h4 {
    font-size: 30px;
  }
}

.contact .existing-customer {
  position: relative;
  padding: 25px 30px;
  background-color: var(--color-white);
  border-radius: 35px;
  margin-bottom: 40px;
}

.contact .existing-customer h5 {
  font-size: 24px;
  margin-bottom: 8px;
}

.contact .contact-bg {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  opacity: 0.15;
}

.contact .contact-bg svg {
  width: 100%;
  height: 100%;
}

.auth {
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
}

.auth .site-log {
  width: 180px;
}

.auth .auth-left {
  background-size: cover;
  background-repeat: no-repeat;
  max-width: 100%;
  width: 100%;
  min-height: 100dvh;
  max-height: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: conic-gradient(
    from 45deg,
    rgba(170, 204, 255, 1),
    rgba(222, 166, 255, 1),
    rgba(170, 204, 255, 1)
  );
  border-radius: 30px;
  position: relative;
}




@media (max-width: 991px) {
  .auth .auth-left {
    min-height: -webkit-fit-content;
    min-height: -moz-fit-content;
    min-height: fit-content;
  }
}

.auth .auth-left .auth-left-content {
  padding: 70px;
  max-width: 100%;
  width: 100%;
}

@media (max-width: 1399.98px) and (min-width: 1200px) {
  .auth .auth-left .auth-left-content {
    padding: 60px 40px;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .auth .auth-left .auth-left-content {
    padding: 0 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .auth .auth-left .auth-left-content {
    padding: 60px;
  }
}

@media (max-width: 991px) {
  .auth .auth-right {
    min-height: 100% !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .auth .auth-left .auth-left-content {
    padding: 60px;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .auth .auth-left .auth-left-content {
    padding: 60px 50px;
  }
}

@media (max-width: 576px) {
  .auth .auth-left .auth-left-content {
    padding: 40px 20px;
  }
}

.auth .auth-left .auth-left-content .auth-slider-wrapper {
  width: 100%;
}

.auth .auth-left .auth-left-content .auth-slider-wrapper .auth-slider {
  padding-bottom: 60px;
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .auth-slider-item {
  text-align: center;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .auth
    .auth-left
    .auth-left-content
    .auth-slider-wrapper
    .auth-slider
    .auth-slider-item {
    padding: 0;
  }
}

@media (max-width: 576px) {
  .auth
    .auth-left
    .auth-left-content
    .auth-slider-wrapper
    .auth-slider
    .auth-slider-item {
    padding: 0;
  }
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .auth-slider-item
  h4 {
  font-size: 34px;
  margin-bottom: 20px;
  color: var(--color-white);
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .auth-slider-item
  p {
  opacity: 0.8;
  color: var(--color-white);
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-pagination-bullet {
  width: var(
    --swiper-pagination-bullet-width,
    var(--swiper-pagination-bullet-size, 12px)
  ) !important;
  height: var(
    --swiper-pagination-bullet-height,
    var(--swiper-pagination-bullet-size, 12px)
  );
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 20px) !important;
  background: rgba(255,255,255,1) !important;
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: rgba(255,255,255,1) !important;
  width: 50px !important;
}

.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-horizontal
  > .swiper-pagination-bullets,
.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-pagination-bullets.swiper-pagination-horizontal,
.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-pagination-custom,
.auth
  .auth-left
  .auth-left-content
  .auth-slider-wrapper
  .auth-slider
  .swiper-pagination-fraction {
  bottom: var(--swiper-pagination-bottom, 5px) !important;
}

.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap,8px);
}

.auth .auth-right {
  padding: 60px 0;
  position: relative;
  background-color: var(--color-white);
  min-height: 100vh;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;
}

.auth .auth-right .auth-back {
  position: absolute;
  right: 60px;
  top: 60px;
  z-index: 5;
}

.auth .auth-right .auth-back a {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  line-height: 1;
  padding: 0 25px;
  height: 48px;
  font-size: 15px;
  border-radius: 40px;
  background-color: var(--color-primary-light);
  border: 1px solid var(--color-primary);
  color: var(--text-primary);
  transition: all linear 0.3s;
}

.auth .auth-right .auth-back a:hover {
  background-color: var(--color-primary);
  color: var(--color-primary-text);
  text-decoration: underline;
}

.auth .auth-right .auth-back a i {
  font-size: 24px;
}

.auth-wrapper{
  padding: 20px;
}

.auth .auth-right .auth-content {
  width: 100%;
  padding: 0px 10%;
  text-align: center;
  position: relative;
  z-index: 5;
}
@media (max-width: 1399.98px) and (min-width: 1200px) {
  .auth .auth-right .auth-content {
    width: 100%;
  }
}

@media (max-width: 1199px){
  .auth .auth-right .auth-content {
    width: 100%;
    padding: 0px 4%;
    text-align: center;
    position: relative;
    z-index: 5;
  }
}

.auth .auth-right .auth-content > h2 {
  font-size: 42px;
  font-weight: 600;
  margin-bottom: 12px;
}

@media (min-width: 576px) and (max-width: 767px) {
  .auth .auth-right .auth-content > h2 {
    font-size: 36px;
  }
}

@media (max-width: 576px) {
  .auth .auth-right .auth-content > h2 {
    font-size: 26px;
  }
}

.auth .auth-right .auth-content > p {
  font-size: 18px;
}

@media (max-width: 767px) { 
  .auth .auth-right .auth-content > p {
    font-size: 16px;
  }
}
 
.auth-form {
  margin-top: 45px;
  display: grid;
  gap: 25px;
}

.auth-form .auth-input {
  width: 100%;
  position: relative;
  text-align: left;
}

.auth-form .auth-input label{
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.auth-form .auth-input > input {
  width: 100%;
  padding: 0 40px 0 20px;
  height: 55px;
  border-radius: 60px;
  border: 1px solid var(--border-one);
  background: transparent;
  transition: all linear 0.3s;
}

.auth-form .auth-input > input:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--color-primary-light);
}

.auth-form .auth-input > .auth-input-icon {
  position: absolute;
  top: 44px;
  right: 20px;
  font-size: 17px;
  cursor: pointer;
  opacity: 0.75;
}

.auth-form .auth-checkbox {
  display: flex;
  align-items: flex-start;
  line-height: 1;
  cursor: pointer;
}

.auth-form .auth-checkbox input[type="checkbox"] {
  flex: 0 0 18px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--color-primary);
  border: 1px solid var(--border-light);
}

.auth-form .auth-checkbox label {
  padding-left: 8px;
  cursor: pointer;
  line-height: 1.5;
  font-size: 15px;
  margin-top: -3px;
}

.auth-form .forget-pass {
  color: var(--color-primary);
}

.auth-form .forget-pass:hover {
  text-decoration: underline;
}

.auth-form.otp-form {
  gap: 30px;
}

.auth-form.otp-form .otp-field {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.auth-form.otp-form .otp-field > input {
  width: 55px;
  height: 55px;
  font-size: 18px;
  padding: 10px;
  text-align: center;
  border-radius: 8px;
  border: 1px solid var(--color-primary);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 35px;
  background: transparent;
  transition: all linear 0.3s;
  cursor: pointer;
}

.auth-form.otp-form .otp-field > input:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--color-primary-light);
}

.captcha-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.captcha-wrapper > a {
  display: flex;
  align-items: center;
  gap: 20px;
}

.captcha-wrapper .captcha-img {
  width: 140px;
}

.captcha-wrapper .captcha-img > img {
  width: 100%;
}

.captcha-change {
  font-size: 35px;
  color: var(--color-primary);
  cursor: pointer;
}

.captcha-input {
  width: 100%;
}

.captcha-input > input {
  width: 100%;
  padding: 0 50px 0 20px;
  height: 55px;
  border-radius: 60px;
  border: 1px solid var(--border-light);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 35px;
  background: transparent;
  transition: all linear 0.3s;
}

.captcha-input > input:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--color-primary-light);
}

.auth-input .select2-container--default .select2-selection--single {
  background-color: transparent;
  border: 1px solid var(--border-light);
  border-radius: 60px;
  height: 55px;
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--border-one);
}

.auth-input
  .select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 55px;
  display: flex;
  align-items: center;
  max-width: 100%;
  width: 100%;
  padding: 0 40px 0 20px;
  color: var(--text-secondary);
}

.auth-input
  .select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  top: 50%;
  transform: translateY(-50%);
}

.auth-input
  .select2-container--default.select2-container--open.select2-container--below
  .select2-selection--single,
.auth-input
  .select2-container--default.select2-container--open.select2-container--below
  .select2-selection--multiple {
  border-bottom-left-radius: 60px;
  border-bottom-right-radius: 60px;
  border-top-left-radius: 60px;
  border-top-right-radius: 60px;
}

.auth-input .select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin-left: 0px !important;
}

.auth-input .select2-container--open .select2-dropdown {
  left: 0;
  top: 5px !important;
}

.or {
  margin: 40px 0;
  text-align: center;
  display: inline-block;
  font-size: 18px;
  line-height: 1;
  font-weight: 500;
  position: relative;
}

.or::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: var(--border-one);
  top: 50%;
  transform: translateY(-50%);
  left: calc(100% + 15px);
  display: block;
}

.or::after {
  content: "";
  position: absolute;
  width: 100%;
  display: block;
  height: 1px;
  background: var(--border-one);
  top: 50%;
  transform: translateY(-50%);
  right: calc(100% + 15px);
}

.sign-option {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: center;
  gap: 20px;
}

@media (max-width: 576px) {
  .sign-option {
    grid-template-columns: repeat(1, 1fr);
  }
}

.sign-option a {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 0 20px;
  height: 48px;
  border: 1px solid var(--border-one);
  border-radius: 60px;
  color: var(--text-primary);
  transition: all linear 0.3s;
}

.sign-option a:hover {
  text-decoration: underline;
}

.sign-option a:is(.facebook):hover {
  color: #1877f2;
}

.sign-option a:is(.facebook) i {
  color: #1877f2;
}

.sign-option a:is(.google) i {
  color: #ff3e30;
}

.sign-option a:is(.google):hover {
  color: #ff3e30;
}

.sign-option a i {
  font-size: 18px;
  line-height: 1.1;
}

.have-account {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}

.have-account p {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 60px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.have-account p a {
  color: var(--color-primary);
}

.have-account p a:hover {
  text-decoration: underline;
}

.auth .auth-right .auth-shape {
  position: absolute;
  -webkit-filter: blur(10px);
  filter: blur(10px);
  opacity: 0.5;
}

.auth .auth-right .auth-shape.auth-shape-2 {
  width: 300px;
  left: 0;
  top: 100px;
}

.auth .auth-right .auth-shape.auth-shape-1 {
  width: 400px;
  bottom: 100px;
  right: 0;
}

.auth .auth-right .glass-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0;
  background-color: var(--color-white);
  z-index: 2;
}

.auth .auth-right::after {
  content: "";
  position: absolute;
  width: 600px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  -webkit-filter: blur(200px);
  filter: blur(200px);
  background-color: var(--color-secondary);
  opacity: 0.6;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.recapture {
  height: 100dvh;
  width: 100%;
  position: relative;
  z-index: 1;
}

.recapture .recapture-wrapper {
  height: 100dvh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recapture .recapture-wrapper .recapture-content {
  width: 500px;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  background: var(--color-white);
  padding: 40px 30px;
  border-radius: 8px;
  border: 1px solid var(--color-primary-light);
}

@media (max-width: 576px) {
  .recapture .recapture-wrapper .recapture-content {
    width: 90%;
    padding: 30px 20px;
  }
}

.recapture .recapture-wrapper .recapture-content p {
  padding-bottom: 12px;
}

.recapture .recapture-wrapper .recapture-content .recapture-img {
  border: 1px solid var(--color-primary-soft);
  border-radius: 4px;
  width: 230px;
  display: inline-block;
}

@media (max-width: 576px) {
  .recapture .recapture-wrapper .recapture-content .recapture-img {
    width: 160px;
  }
}

.recapture .recapture-wrapper .recapture-content .recapture-reload {
  width: 40px;
  aspect-ratio: 1/1;
  background-color: transparent;
}

@media (max-width: 576px) {
  .recapture .recapture-wrapper .recapture-content .recapture-reload {
    width: 30px;
  }
}

.recapture .recapture-wrapper .recapture-content .recapture-reload:hover svg {
  fill: var(--text-primary);
}

.recapture .recapture-wrapper .recapture-content .recapture-reload svg {
  width: 100%;
  height: 100%;
  fill: var(--text-secondary);
  transition: 0.3s;
}
.recapture .recapture-wrapper .recapture-content .recapture-form {
  margin-top: 40px;
  display: flex;
  align-items: center;
  gap: 20px;
}

@media (max-width: 576px) {
  .recapture .recapture-wrapper .recapture-content .recapture-form {
    flex-direction: column;
  }

  .recapture
    .recapture-wrapper
    .recapture-content
    .recapture-form
    .i-btn.btn--lg {
    width: 100%;
  }
}
.recapture .recapture-wrapper .recapture-content .recapture-form input {
  width: 100%;
  padding: 0 20px;
  height: 48px;
  border-radius: 60px;
  border: 1px solid var(--border-light);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 35px;
  background: transparent;
  transition: all linear 0.3s;
}

.recapture .recapture-wrapper .recapture-content .recapture-form input:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--color-primary-light);
}

.recapture .recapture-bg {
  position: absolute;
  inset: 0;
  z-index: -1;
  opacity: 0.08;
  width: 100%;
  height: 100%;
}

.recapture .recapture-bg img {
  width: 100%;
  height: 100%;
}

.error-content {
  text-align: center;
}
.error-content > h1 {
  font-size: 100px;
  font-weight: 700;
  line-height: 1.1;
}
.error-content > p {
  font-size: 30px;
}

.error-content .i-btn.btn--lg {
  padding: 20px 45px;
  font-size: 18px;
}

@media (max-width: 767px) {
  .error-content > h1 {
    font-size: 80px;
  }
  .error-content > p {
    font-size: 20px;
  }
  .error-content .i-btn.btn--lg {
    padding: 15px 30px;
    font-size: 16px;
  }
}

.vbox-close {
  top: 50px;
  right: 50px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-item{
  position: relative;
  background-color: var(--card-bg-light);
  padding: 35px;
  border-radius: 40px;
  z-index: 1;
}
.template-item  .category {
  padding: 6px 20px;
  border: 1px solid var(--color-primary-light-2);
  text-align: center;
  line-height: 1;
  font-size: 14px;
  color: var(--color-primary);
  display: inline-block;
  border-radius: 30px;
  background: var(--color-white);
  position: relative;

  &::after, &::before{
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--color-primary-light-2);
    border-radius: 50%;
  }
  &::before{
    left: 8px;
  }
  &::after{
    right: 8px;
  }
}

.template-item::before{
  content: '';
  position: absolute;
  width: 5px;
  height: 100%;
  background-color: var(--color-primary);
  display: block;
  left: 0;
  top: 0px;
  transform: scale(0);
  transition: 0.4s ease;
  z-index: -1;
}
.template-item:hover::before{
  transform: scale(0.7);
}
.template-item .radius-one{
  position: absolute;
  right: 65px;
  top: -10px;
  transform: rotate(270deg);
}

.template-item .radius-two {
  position: absolute;
  right: -8px;
  top: 47px;
  transform: rotate(-112deg);
}
@media (max-width: 768px) {
  .template-item .radius-one {
    position: absolute;
    right: 55px;
    top: -9px;
  }
  .template-item .radius-two {
    position: absolute;
    right: -10px;
    top: 40px;
  }
}
.template-item .content {
  padding-right: 55px;
}
.template-item .content h4{
  font-size: 24px;
  margin-bottom: 15px;
}
.template-item .content p{
  font-size: 16px;
  margin-bottom: 0;
  line-height: 1.6;
}
.template-item .icon{
  position: absolute;
  right: 0;
  top: -8px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 8px solid var(--color-white);
}
.template-item .icon i{
  color: var(--color-white);
  font-size: 30px;
  vertical-align: middle;
}
@media (max-width:768px) {
  .template-item{
    padding: 25px;
  }
  .template-item .content h4{
    font-size: 20px;
  }
  .template-item .content {
    padding-right: 45px;
  }
  .template-item .icon{
    width: 65px;
    height: 65px;
  }
  .template-item .icon i{
    font-size: 26px;
  }
  .template-item .content p{
    font-size: 15px;
  }
}

.pricing-item{
  position: relative;
  background-color: var(--card-bg-light);
  padding: 30px;
  border-radius: 40px;
  margin-bottom: 15px;
}
.pricing-item:hover .i-btn{
  transform: translateX(20px);
}
.pricing-item .radius-one{
  position: absolute;
  right: 68px;
  top: -10px;
  transform: rotate(277deg);
  z-index: 2;
}
.pricing-item .radius-two {
  position: absolute;
  right: -14px;
  top: 54px;
  transform: rotate(-204deg);
  z-index: 2;
  width: 40px;
  height: 40px;
}
.pricing-item .icon {
  position: absolute;
  right: -6px;
  top: -12px;
  width: 85px;
  height: 85px;
  border-radius: 50%;
  background-color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 11px solid var(--color-white);
}
.pricing-item .icon i{
  color: var(--color-white);
  font-size: 30px;
  vertical-align: middle;
}
.pricing-header{
  padding-right: 50px;
}

.pricing-header span{
  display: inline-block;
  font-size: 18px;
  margin-bottom: 5px;
}
.pricing-header h5{
  font-size: 32px;
  margin-bottom: 5px;
}
.pricing-item .price{
  margin: 30px 0;
}
.pricing-item .price h3{
  font-size: 40px;
}
.pricing-item .price span{
  display: inline-block;
  font-size: 18px;
  color: var(--text-secondary);
}
.pricing-item .price del{
  display: block;
  font-size: 18px;
  color: #b0b0b0;
}
.pricing-item .body h6{
  font-size: 18px;
  margin-bottom: 18px;
}
.pricing-item .body{
  margin-bottom: 60px;
}
.pricing-item .body ul li{
  margin-bottom: 18px;
  font-size: 16px;
}
.pricing-item .body ul li:last-child{
  margin-bottom: 0px;
}
.pricing-item .body ul li span{
  display: inline-flex;
  justify-content: center;
  align-items: center;
  min-width: 22px;
  height: 22px;
  background-color: var(--color-primary);
  border-radius: 50%;
  margin-right: 10px;
  line-height: 1;
}
.pricing-item .body ul li span i{
  color: var(--color-white);
  font-size: 18px;
  vertical-align: middle;
}
.pricing-item .i-btn{
  position: absolute;
  left: -10px;
  bottom: -15px;
}

@media (max-width: 768px) {
  .pricing-item{
    padding: 20px;
  }
  .pricing-header span {
    font-size: 15px;
  }
  .pricing-header h5 {
    font-size: 24px;
  }
  .pricing-item .price h3 {
    font-size: 34px;
  }
  .pricing-item .price span {
    font-size: 15px;
  }
  .pricing-item .body h6 {
    font-size: 15px;
  }
  .pricing-item .body ul li {
    font-size: 15px;
  }
  .pricing-item .body ul li span {
    min-width: 20px;
    height: 20px;
  }
  .pricing-item .body ul li span i {
    font-size: 15px;
  }
}

.pricing-item.style-dark{
  background-color: var(--text-primary);
}
.pricing-item.style-dark .pricing-header span{
  color: var(--color-white);
}
.pricing-item.style-dark .pricing-header h5{
  color: var(--color-white);
}
.pricing-item.style-dark .pricing-header p{
  color: var(--color-white);
}
.pricing-item.style-dark .icon{
  background-color: var(--text-primary);
}
.pricing-item.style-dark .icon i{
  color: var(--color-secondary);
}

.pricing-item.style-dark .price h3{
  color: var(--color-secondary);
}
.pricing-item.style-dark .price span{
  color: var(--color-secondary);
}
.pricing-item.style-dark .body h6{
  color: var(--color-white);
}

.pricing-item.style-dark .body ul li{
  color: var(--color-white);
}
.pricing-item.style-dark .body ul li span{
  background-color: var(--color-secondary);
}
.pricing-item.style-dark .body ul li span i{
  color: var(--text-primary);
}
.pricing-item.style-dark .pricing-header span{
  background-color: var(--color-secondary);
  color: var(--color-white);
  font-size: 16px;
  padding: 6px 34px;
  transform: translate(-30px, -30px);
  border-radius: 40px 0;
}

@media (max-width: 768px) {
  .pricing-item.style-dark .pricing-header span {
    transform: translate(-20px, -20px);
  }
}

.service-item{
  position: relative;
  background-color: var(--card-bg-light);
  padding: 30px;
  border-radius: 40px;
  margin-bottom: 15px;
  z-index: 1;
}
.service-item::before{
  content: '';
  position: absolute;
  width: 5px;
  height: 100%;
  background-color: var(--color-primary);
  display: block;
  left: 0;
  top: 0px;
  transform: scale(0);
  transition: 0.4s ease;
  z-index: -1;
}
.service-item:hover::before{
  transform: scale(0.7);
}
.service-item .image{
  margin-bottom: 25px;
  max-width: 120px;
}
.service-item h4{
  margin-bottom: 10px;
}
.service-item p{
  margin-bottom: 40px;
}
.service-item .i-btn{
  position: absolute;
  left: -10px;
  bottom: -15px;
}
.service-wrapper{
  position: relative;
  z-index: 1;
}
.service-wrapper svg{
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 100%;
  @media (max-width: 1399px){
   left: 32px;
  }
  path{
    stroke: var(--color-primary);
  }
}


.service-type-icon{
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 50%;
  border: 2px solid var(--color-primary);
  text-align: center;
  background-color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;

}

@media (max-width: 991px) {
  .service-type-icon{
    display: none;
    visibility: hidden;
  }
}
.service-type-icon i{
  font-size: 36px;
  color: var(--color-primary);
  @media (max-width:1399px) and (min-width:992px) {
    font-size: 26px;
  }
}

.h-625{
  height: 625px !important;
}
@media (max-width: 1199px) {
  .service-wrapper svg{
    display: none;
    visibility: hidden;
  }
  .h-625{
    height: auto !important;
  }
}
.service-tab-wrapper{
  padding: 40px;
  border-radius: 40px;
  border: 1px solid var(--border-two);
  background-color: rgba(234, 236, 240, 1);
}

@media (max-width: 991px) {
  .service-tab-wrapper{
    padding: 20px;
  }
}

.service-d-image img{
  border-radius: 30px;
}

.service-details ul{
  margin-top: 35px;
  margin-bottom: 40px;
}

.service-details ul li{
  margin-bottom: 6px;
  font-weight: 500;
}

.service-details ul li:last-child{
  margin-bottom: 0;
}

.service-details ul li i{
  font-size: 20px;
  margin-right: 10px;
  vertical-align: middle;
  color: var(--text-primary);
}

input,textarea{
  width: 100%;
  padding: 10px 15px;
  font-size: 15px;
}
textarea{
  min-height: 100px;
}
.form-inner{
  margin-bottom: 20px;
} 

.sidebar-wigt {
  padding: 30px;
  border-radius: 30px;
  background-color: var(--card-bg-light);
}
.sidebar-wigt h4{
  font-size: 22px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
.sidebar-wigt ul li{
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 14px;
}
.sidebar-wigt ul li:last-child{
  margin-bottom: 0;
}
.sidebar-wigt ul li a{
  color: inherit;
  transition: 0.3s ease;
}
.sidebar-wigt ul li:hover a{
  color: var(--color-primary);
}
.sidebar-wigt ul li i{
  font-size: 15px;
  vertical-align: middle;
  margin-right: 8px;
}

.power-feature-list li{
  margin-bottom: 50px;
  display: flex;
  justify-content: flex-start;
  gap: 15px;
  align-items: flex-start;
  transition: 0.45s ease;
}

.power-feature-list li .icon{
  min-width: 50px;
  height: 50px;
  background-color: var(--color-primary-light);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.45s ease;
}
.power-feature-list li .icon i{
  vertical-align: middle;
  line-height: 1;
  transition: 0.45s ease;
}

.power-feature-list li:hover{
  transform: translateX(10px);
}
.power-feature-list li:hover .icon{
  background-color: var(--color-primary);
}
.power-feature-list li:hover .icon i{
  color: var(--color-white);
}

@media (max-width: 991px) {
  .power-feature-list li:nth-child(2){
    padding-left: 0px;
  }
  .power-feature-list li{
    margin-bottom: 35px;
  }
}
.power-feature-list li:last-child{
  margin-bottom: 0px;
}
.power-feature-list li .icon i{
  color: var(--color-primary);
  font-size: 21px;
}

.counter-wrapper{
  background-color: var(--text-primary);
  padding: 30px;
  border-radius: 30px;
}

.counter-single{
  padding: 20px 0px;
}
.counter-single h3{
  color: var(--color-white);
  font-size: 50px;
}
.counter-single i{
  color: var(--color-white);
  font-size: 30px;
}
.counter-single p{
  margin-bottom: 0;
  color: var(--color-white);
  font-size: 16px;
  opacity: 0.6;
  font-weight: 400;
}

.about-section{
  overflow: hidden;
}

.about-card-wrapper{
  position: relative;
}
.about-card-wrapper .center-icon{
  position: absolute;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: -1;
  background: linear-gradient(90deg,rgb(126, 212, 246), rgba(216, 137, 255, 0.94));
}

@media (max-width: 767px) {
  .about-card-wrapper .center-icon{
    display: none;
    visibility: hidden;
  }
}

.about-card-wrapper .row .col-md-6:nth-child(1) .about-card-item{
  z-index: -9 !important;
}
.about-card-wrapper .row .col-md-6:nth-child(1) .about-card-item .icon{
  position: absolute;
  left: 0;
  top: -8px;
}
.about-card-wrapper .row .col-md-6:nth-child(1) .about-card-item .content{
  padding-left: 40px;
}
.about-card-wrapper .row .col-md-6:nth-child(2) .about-card-item .icon{
  position: absolute;
  right: 0;
  top: -8px;
}
.about-card-wrapper .row .col-md-6:nth-child(2) .about-card-item .content{
  padding-right: 40px;
}

.about-card-wrapper .row .col-md-6:nth-child(3) .about-card-item .icon{
  position: absolute;
  left: 0;
  bottom: -8px;
}
.about-card-wrapper .row .col-md-6:nth-child(3) .about-card-item .content{
  padding-left: 40px;
}
.about-card-wrapper .row .col-md-6:nth-child(4) .about-card-item .icon{
  position: absolute;
  right: 0;
  bottom: -8px;
}
.about-card-wrapper .row .col-md-6:nth-child(4) .about-card-item .content{
  padding-right: 40px;
}

.about-card-wrapper .center-icon svg{
  fill: var(--color-white);
  width: 40px;
  height: 40px;
}
.about-card-item{
  position: relative;
  background-color: var(--card-bg-light);
  padding: 40px;
  border-radius: 40px;
  z-index: 1;
}
@media (min-width: 991px) and (max-width: 1199px) {
  .about-card-item{
    padding: 25px;
  }
}

.about-card-item .content{
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.about-card-item .content h4{
  font-size: 24px;
  margin-bottom: 0px;
}
.about-card-item .content p{
  font-size: 16px;
  margin-bottom: 0;
  line-height: 1.6;
}
.about-card-item .icon{
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  border: 8px solid var(--color-white);
}
.about-card-item .icon i{
  color: var(--color-white);
  font-size: 26px;
  vertical-align: middle;
}
@media (max-width:768px) {
  .about-card-item{
    padding: 25px;
  }
  .about-card-item .content h4{
    font-size: 20px;
  }
  .about-card-item .icon{
    width: 65px;
    height: 65px;
  }
  .about-card-item .icon i{
    font-size: 26px;
  }
  .about-card-item .content p{
    font-size: 15px;
  }
}

.feed-item{
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 20px;
  background: linear-gradient(90deg,rgb(126, 212, 246), rgba(216, 137, 255, 0.94));
  border-radius: 20px;
  padding: 25px;
}

.feed-item .serial{
  font-size: 20px;
  min-width: 45px;
  height: 45px;
  line-height: 45px;
  border: 1px solid var(--color-white);
  color: var(--color-white);
  text-align: center;
  border-radius: 50%;
}
.feed-item .text p{
  color: var(--color-white);
  margin-bottom: 0;
  font-size: 18px;
}
@media (max-width:768px) {
  .feed-item{
    padding: 25px;
  }
  .feed-item .serial{
    min-width: 30px;
    height: 30px;
    line-height: 30px;
  }
  .feed-item .text p{
    font-size: 16px;
    font-weight: 400;
  }
}

.arrow-two{
  position: absolute;
  left: -35%;
  top: -60px;
  max-width: 120px;
  animation: bounce 1s linear infinite alternate;
}

.arrow-one{
  position: absolute;
  left: -35%;
  top: 70px;
  max-width: 120px;
  animation: bounce 1s linear infinite alternate;
}
@keyframes bounce {
  0%{
    transform: translate(10px);
  }
  100%{
    transform: translateY(-10px);
  }
}

@media (max-width:991px) {
  .arrow-one{
    display: none;
    visibility: hidden;
  }
  
  .arrow-two{
    display: none;
    visibility: hidden;
  }
}

.star-one{
  position: absolute;
  left: -70%;
  top: -20px;
  max-width: 100px;
}
.star-six {
  position: absolute;
  right: -50%;
  top: 50px;
  width: 70px;
}
.star-five{
  position: absolute; 
  right: -70%; 
}
.star-two {
  position: absolute;
  right: -50%;
  bottom: 20px;
  width: 80px;
}

.nav-tabs.style-7{
  border-bottom: none;
}

.nav-tabs.style-7 .nav-item .nav-link{
  color: var(--text-primary) !important;
  background-color: var(--color-white) !important;
  border: 2px solid var(--border-two) !important;
  border-radius: 30px;
  transition: 0.4s ease;
  font-size: 15px;
}

.nav-tabs.style-7 .nav-item .nav-link.active{
  background-color: var(--color-primary) !important;
  color: var(--color-white) !important;
}
.nav-tabs.style-7 .nav-item .nav-link.active span{
  background-color: var(--color-white) !important;
}
.nav-tabs.style-7 .nav-item .nav-link:hover{
  background-color: var(--color-primary) !important;
  color: var(--color-white) !important;
}
.nav-tabs.style-7 .nav-item .nav-link:hover span{
  background-color: var(--color-white) !important;
}

.nav-tabs.style-7 .nav-item .nav-link span{
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 23px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .08);
  transition: 0.4s ease;
}
.nav-tabs.style-7 .nav-item .nav-link span i{
  font-size: 14px;
  color: rgba(88, 176, 254, 1);
  vertical-align: middle;
  transition: 0.4s ease;
  transform: rotate(0deg);
}
.contact-left-img{
  max-width: 220px;
  margin-bottom: 40px;
}

.payment-image{
  width: 100%;
  max-width: 400px;
}

@media (max-width: 991px) {
  .payment-image{
    max-width: 100%;
    text-align: center;

    img{
      max-width: 500px;
      margin: 0 auto;
    }
  }
}

@media (max-width: 991px) {
  .payment-image{
    img{
      max-width: 100%;
      margin: 0 auto;
    }
  }
}

.language button img{
  min-width: 40px;
  max-width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 50%;
}

@media (max-width: 991px) {
  
.language button img{
  min-width: 30px;
  max-width: 30px;
  height: 30px;
}
}

.language .dropdown-menu li a img{
  width: 25px;
  height: auto;
}


.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination .page-item .page-link {
  width: 45px;
  height: 45px;
  line-height: 1;
  font-size: 17px;
  aspect-ratio: 1/1;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: var(--text-secondary);
  background: var(--color-primary-light);
  border: none;
  border-radius: 8px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  -o-border-radius: 8px;
  transition: 0.3s;
}
.pagination .page-item .page-link:hover {
  background: var(--color-primary);
  color: var(--color-white);
}
.page-item.active > .page-link {
  background: var(--color-primary);
  color: var(--color-white);
}

@media (max-width:991px) {
  .pagination .page-item .page-link {
    width: 35px;
    height: 35px;
  }
}

.modal-delete-noti {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 30px 0;
}

.notification-modal-icon {
  max-width: 100;
  width: 45px;
  height: 45px;
  background: var(--color-primary-light);
  border-radius: 50%;
  line-height: 45px;
  position: relative;
  margin: 15px;
}

.notification-modal-icon::before {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--color-primary-light);
  display: block;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.notification-modal-icon::after {
  content: '';
  position: absolute;
  width: 75px;
  height: 75px;
  background: var(--color-primary-light);
  display: block;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.notification-modal-icon i {
  font-size: 24px;
  color: var(--color-danger);
}

.notification-modal-content {
  margin-top: 25px;
}

.notification-modal-content>h5 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 10px;
}

.notification-modal .modal-footer {
  justify-content: center;
  gap: 10px;
  border-color: var(--border-one);
  padding: 10px 0 0 0;
}

.team-item img{
  border-radius: 20px;
}

.glider-nav {
  position: relative !important;
  width: 100%;
  z-index: 1;

  .nav-link {
    position: relative;
    padding: 10px 20px;
    cursor: pointer;
    text-align: center;
    transition: color 0.3s ease-in-out;
    min-width: 100px;
    font-size: 14px;
  }
  
  .nav-link.active {
    color: var(--color-white);
    @media (max-width:374px) {
      color: var(--color-primary);
      font-weight: 500;
    }
  }
  
  .glider {
    position: absolute;
    bottom: 4px;
    left: 8px;
    height: 35px;
    width: 100px;
    background-color: var(--color-primary);
    transition: transform 0.3s ease-in-out;
    border-radius: 50px;
    z-index: -1;
  }
}

@media (width <= 370px) {
  .glider{
    display: none;
    visibility: hidden;
  }
}




.service-slider-one,
.service-slider-two{
  padding-inline: 10px;
  margin-inline: -10px;
}




