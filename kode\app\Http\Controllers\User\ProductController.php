<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Services\External\NazmartProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    private $productService;

    public function __construct(NazmartProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Display the product creation form
     */
    public function create()
    {
        $user = auth_user();
        
        // Check if user has a linked Nazmart store
        if (!$user->nazmart_store_id) {
            return redirect()->route('admin.store.create')
                ->with('error', translate('Please create a Nazmart store first before adding products.'));
        }

        // Get categories for the user's store
        $categoriesResult = $this->productService->getCategories($user->nazmart_store_id);
        $categories = $categoriesResult['success'] ? $categoriesResult['data'] : [];

        return view('user.product.create', [
            'meta_data' => $this->metaData(['title' => translate("Create Product")]),
            'categories' => $categories,
            'user_store' => [
                'id' => $user->nazmart_store_id,
                'slug' => $user->nazmart_store_slug,
                'url' => $user->nazmart_store_url
            ]
        ]);
    }

    /**
     * Handle product creation request
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_name' => 'required|string|max:255',
                'price' => 'required|numeric|min:0',
                'category' => 'required|integer',
                'description' => 'required|string|min:10',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = auth_user();
            
            if (!$user->nazmart_store_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'No Nazmart store linked to your account'
                ], 400);
            }

            $productData = [
                'product_name' => $request->product_name,
                'price' => $request->price,
                'category' => $request->category,
                'description' => $request->description,
                'vendor_id' => $user->nazmart_store_id
            ];

            // Handle image upload if provided
            if ($request->hasFile('image')) {
                $imageUploadResult = $this->productService->uploadImage(
                    $user->nazmart_store_id,
                    $request->file('image')
                );

                if ($imageUploadResult['success']) {
                    $productData['image'] = $imageUploadResult['data']['image_id'] ?? null;
                }
            }

            // Create product via API
            $result = $this->productService->createProduct($productData);

            if ($result['success']) {
                // Store product information for potential social media posting
                $productInfo = $result['data'];
                
                // Check if user wants to post this product after upload
                if ($request->post_after_upload) {
                    // Redirect to post creation with product data
                    return response()->json([
                        'success' => true,
                        'message' => $result['message'],
                        'data' => $result['data'],
                        'redirect_to_post' => true,
                        'product_url' => $productInfo['product_url'] ?? null
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'errors' => $result['data']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the product list
     */
    public function index()
    {
        $user = auth_user();
        
        if (!$user->nazmart_store_id) {
            return redirect()->route('admin.store.create')
                ->with('error', translate('Please create a Nazmart store first.'));
        }

        return view('user.product.index', [
            'meta_data' => $this->metaData(['title' => translate("My Products")]),
            'user_store' => [
                'id' => $user->nazmart_store_id,
                'slug' => $user->nazmart_store_slug,
                'url' => $user->nazmart_store_url
            ]
        ]);
    }

    /**
     * Get categories for AJAX requests
     */
    public function getCategories()
    {
        $user = auth_user();
        
        if (!$user->nazmart_store_id) {
            return response()->json([
                'success' => false,
                'message' => 'No store linked'
            ], 400);
        }

        $result = $this->productService->getCategories($user->nazmart_store_id);
        
        return response()->json($result);
    }

    /**
     * Upload image for AJAX requests
     */
    public function uploadImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid image file',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = auth_user();
            
            if (!$user->nazmart_store_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'No store linked'
                ], 400);
            }

            $result = $this->productService->uploadImage(
                $user->nazmart_store_id,
                $request->file('image')
            );

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while uploading image: ' . $e->getMessage()
            ], 500);
        }
    }
}
