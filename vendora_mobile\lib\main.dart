import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
// import 'core/routes/app_router.dart'; // TODO: Implement router
import 'core/di/injection_container.dart' as di;
import 'presentation/providers/auth_provider.dart';
import 'presentation/providers/dashboard_provider.dart';
import 'presentation/providers/post_provider.dart';
import 'presentation/providers/store_provider.dart';
import 'presentation/providers/ai_assistant_provider.dart';
import 'presentation/providers/create_post_provider.dart';
import 'presentation/screens/create_post/create_post_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Initialize dependency injection
  await di.init();

  runApp(const VendoraMobileApp());
}

class VendoraMobileApp extends StatelessWidget {
  const VendoraMobileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => di.sl<AuthProvider>()),
        ChangeNotifierProvider(create: (_) => di.sl<DashboardProvider>()),
        ChangeNotifierProvider(create: (_) => di.sl<PostProvider>()),
        ChangeNotifierProvider(create: (_) => di.sl<StoreProvider>()),
        ChangeNotifierProvider(create: (_) => di.sl<AiAssistantProvider>()),
        ChangeNotifierProvider(create: (_) => di.sl<CreatePostProvider>()),
      ],
      child: MaterialApp(
        title: AppConfig.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const CreatePostScreen(), // TODO: Replace with proper routing
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
