<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Stancl\Tenancy\Database\Models\Tenant;
use Modules\Product\Entities\Product;
use Modules\Product\Entities\ProductInventory;
use Modules\Product\Entities\ProductCategory;
use Modules\Product\Entities\ProductGallery;
use Modules\Product\Entities\ProductTag;
use Modules\Product\Entities\ProductUom;
use Modules\Product\Http\Traits\ProductGlobalTrait;
use Modules\Attributes\Entities\Category;
use Modules\Attributes\Entities\Brand;
use Modules\Attributes\Entities\Unit;
use Illuminate\Support\Str;
use App\Models\Slug;

class ProductManagementController extends Controller
{
    use ProductGlobalTrait;

    /**
     * Create a new product in a specific store
     */
    public function createProduct(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'store_id' => 'required|string',
                'name' => 'required|string|max:255',
                'description' => 'required|string',
                'summery' => 'required|string|max:500', // Note: Nazmart uses 'summery' not 'summary'
                'price' => 'nullable|numeric|min:0',
                'sale_price' => 'required|numeric|min:0',
                'cost' => 'required|numeric|min:0',
                'sku' => 'required|string|max:100',
                'quantity' => 'required|integer|min:0',
                'category_id' => 'required|integer',
                'brand' => 'nullable|integer',
                'unit_id' => 'required|integer',
                'uom' => 'required|string',
                'image_id' => 'nullable|string',
                'product_gallery' => 'nullable|array',
                'tags' => 'nullable|string',
                'min_purchase' => 'nullable|integer|min:1',
                'max_purchase' => 'nullable|integer',
                'is_refundable' => 'nullable|boolean',
                'is_taxable' => 'nullable|boolean',
                'tax_class' => 'nullable|integer',
                'badge_id' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $storeId = $request->input('store_id');

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $productData = null;

            // Switch to tenant context and create product
            $tenant->run(function () use ($request, &$productData) {
                $productData = $this->createProductInTenant($request);
            });

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => $productData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create product within tenant context
     */
    private function createProductInTenant(Request $request)
    {
        try {
            DB::beginTransaction();

            // Prepare product data using the trait method
            $productData = $this->ProductData($request->all());

            // Generate unique slug
            $slug = $request->input('slug') ?? $request->input('name');
            $productData['slug'] = $this->generateUniqueSlug($slug);

            // Create the product
            $product = Product::create($productData);

            // Create slug record
            $product->slug()->create(['slug' => $product->slug]);

            // Create inventory
            $inventoryData = [
                'product_id' => $product->id,
                'sku' => $this->generateUniqueSku($request->input('sku')),
                'stock_count' => $request->input('quantity', 0),
                'sold_count' => 0
            ];
            $inventory = ProductInventory::create($inventoryData);

            // Create product category
            ProductCategory::create([
                'category_id' => $request->input('category_id'),
                'product_id' => $product->id
            ]);

            // Create product UOM
            ProductUom::create([
                'product_id' => $product->id,
                'unit_id' => $request->input('unit_id'),
                'unit_value' => $request->input('uom', '1')
            ]);

            // Handle tags if provided
            if ($request->input('tags')) {
                $this->createProductTags($product->id, $request->input('tags'));
            }

            // Handle gallery images if provided
            if ($request->input('product_gallery')) {
                $this->createProductGallery($product->id, $request->input('product_gallery'));
            }

            DB::commit();

            return [
                'product_id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $inventory->sku,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'stock_count' => $inventory->stock_count,
                'category_id' => $request->input('category_id'),
                'created_at' => $product->created_at
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate unique slug for product
     */
    private function generateUniqueSlug(string $name): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while (Product::where('slug', $slug)->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate unique SKU for product
     */
    private function generateUniqueSku(string $sku): string
    {
        $baseSku = strtoupper($sku);
        $finalSku = $baseSku;
        $counter = 1;

        while (ProductInventory::where('sku', $finalSku)->exists()) {
            $finalSku = "{$baseSku}-{$counter}";
            $counter++;
        }

        return $finalSku;
    }

    /**
     * Create product tags
     */
    private function createProductTags(int $productId, string $tags): void
    {
        $tagArray = explode(',', $tags);
        $tagData = [];

        foreach ($tagArray as $tag) {
            $tagData[] = [
                'product_id' => $productId,
                'tag_id' => trim($tag)
            ];
        }

        if (!empty($tagData)) {
            ProductTag::insert($tagData);
        }
    }

    /**
     * Create product gallery
     */
    private function createProductGallery(int $productId, array $galleryImages): void
    {
        $galleryData = [];

        foreach ($galleryImages as $imageId) {
            $galleryData[] = [
                'product_id' => $productId,
                'image_id' => $imageId
            ];
        }

        if (!empty($galleryData)) {
            ProductGallery::insert($galleryData);
        }
    }

    /**
     * Get products from a specific store
     */
    public function getProducts(Request $request)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $products = null;

            // Switch to tenant context and get products
            $tenant->run(function () use ($request, &$products) {
                $query = Product::with(['category', 'inventory', 'image', 'brand'])
                    ->where('status_id', 1); // Only published products

                // Apply filters
                if ($request->has('category_id')) {
                    $query->whereHas('product_category', function ($q) use ($request) {
                        $q->where('category_id', $request->input('category_id'));
                    });
                }

                if ($request->has('search')) {
                    $search = $request->input('search');
                    $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                            ->orWhere('description', 'like', "%{$search}%");
                    });
                }

                // Pagination
                $perPage = $request->input('per_page', 15);
                $products = $query->paginate($perPage);
            });

            return response()->json([
                'success' => true,
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update product in a specific store
     */
    public function updateProduct(Request $request, $productId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'store_id' => 'required|string',
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string',
                'summery' => 'sometimes|string|max:500',
                'price' => 'sometimes|numeric|min:0',
                'sale_price' => 'sometimes|numeric|min:0',
                'cost' => 'sometimes|numeric|min:0',
                'quantity' => 'sometimes|integer|min:0',
                'category_id' => 'sometimes|integer',
                'brand' => 'sometimes|integer',
                'is_refundable' => 'sometimes|boolean',
                'is_taxable' => 'sometimes|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $storeId = $request->input('store_id');

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $updated = false;

            // Switch to tenant context and update product
            $tenant->run(function () use ($request, $productId, &$updated) {
                $product = Product::find($productId);

                if (!$product) {
                    throw new \Exception('Product not found');
                }

                DB::beginTransaction();

                // Update product data
                $updateData = array_filter($request->only([
                    'name',
                    'description',
                    'summery',
                    'price',
                    'sale_price',
                    'cost',
                    'brand_id',
                    'is_refundable',
                    'is_taxable'
                ]));

                if (!empty($updateData)) {
                    $product->update($updateData);
                }

                // Update inventory if quantity is provided
                if ($request->has('quantity')) {
                    $product->inventory()->update([
                        'stock_count' => $request->input('quantity')
                    ]);
                }

                // Update category if provided
                if ($request->has('category_id')) {
                    $product->product_category()->update([
                        'category_id' => $request->input('category_id')
                    ]);
                }

                DB::commit();
                $updated = true;
            });

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'Product updated successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to update product'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single product from a specific store
     */
    public function getProduct(Request $request, $productId)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $product = null;

            // Switch to tenant context and get product
            $tenant->run(function () use ($productId, &$product) {
                $product = Product::with([
                    'category',
                    'inventory',
                    'image',
                    'brand',
                    'gallery_images',
                    'tag',
                    'uom',
                    'product_category',
                    'product_sub_category'
                ])->find($productId);
            });

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete product from a specific store
     */
    public function deleteProduct(Request $request, $productId)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $deleted = false;

            // Switch to tenant context and delete product
            $tenant->run(function () use ($productId, &$deleted) {
                $product = Product::find($productId);

                if (!$product) {
                    throw new \Exception('Product not found');
                }

                // Soft delete the product
                $deleted = $product->delete();
            });

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Product deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get categories from a specific store
     */
    public function getCategories(Request $request)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $categories = null;

            // Switch to tenant context and get categories
            $tenant->run(function () use (&$categories) {
                $categories = Category::where('status_id', 1)
                    ->select('id', 'name', 'slug', 'description')
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve categories: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get brands from a specific store
     */
    public function getBrands(Request $request)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $brands = null;

            // Switch to tenant context and get brands
            $tenant->run(function () use (&$brands) {
                $brands = Brand::where('status_id', 1)
                    ->select('id', 'name', 'slug', 'description')
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $brands
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve brands: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get units from a specific store
     */
    public function getUnits(Request $request)
    {
        try {
            $storeId = $request->input('store_id');

            if (!$storeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store ID is required'
                ], 400);
            }

            // Find the tenant
            $tenant = Tenant::find($storeId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $units = null;

            // Switch to tenant context and get units
            $tenant->run(function () use (&$units) {
                $units = Unit::where('status_id', 1)
                    ->select('id', 'name', 'short_name')
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $units
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve units: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a simple product with basic information (for BeePost integration)
     */
    public function createSimpleProduct(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_name' => 'required|string|max:255',
                'price' => 'required|numeric|min:0',
                'category' => 'required|integer',
                'description' => 'required|string',
                'image' => 'nullable|string',
                'vendor_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $vendorId = $request->input('vendor_id');

            // Find the tenant (store)
            $tenant = Tenant::find($vendorId);
            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found'
                ], 404);
            }

            $productData = null;

            // Switch to tenant context and create product
            $tenant->run(function () use ($request, &$productData) {
                $productData = $this->createSimpleProductInTenant($request);
            });

            if ($productData) {
                // Generate product URL
                $productUrl = "https://vendora.com/stores/{$tenant->id}/products/{$productData['slug']}";
                $productData['product_url'] = $productUrl;

                return response()->json([
                    'success' => true,
                    'message' => 'Product created successfully',
                    'data' => $productData
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create product'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Simple Product Creation Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the product: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create simple product within tenant context
     */
    private function createSimpleProductInTenant(Request $request)
    {
        try {
            DB::beginTransaction();

            // Get default values for required fields
            $defaultUnit = Unit::first();
            $defaultCategory = Category::find($request->input('category'));

            if (!$defaultUnit) {
                throw new \Exception('No units found in store');
            }

            if (!$defaultCategory) {
                throw new \Exception('Category not found');
            }

            // Prepare basic product data
            $productData = [
                'name' => $request->input('product_name'),
                'summary' => substr($request->input('description'), 0, 500),
                'description' => $request->input('description'),
                'price' => $request->input('price'),
                'sale_price' => $request->input('price'),
                'cost' => $request->input('price') * 0.7, // Default cost as 70% of price
                'status_id' => 2, // Published status
                'image_id' => $request->input('image'),
                'is_refundable' => true,
                'is_taxable' => false,
                'min_purchase' => 1,
                'max_purchase' => null,
                'brand_id' => null,
                'badge_id' => null,
                'tax_class_id' => null
            ];

            // Generate unique slug
            $slug = $request->input('product_name');
            $productData['slug'] = $this->generateUniqueSlug($slug);

            // Create the product
            $product = Product::create($productData);

            // Create slug record
            $product->slug()->create(['slug' => $product->slug]);

            // Generate unique SKU
            $sku = 'PRD-' . strtoupper(substr($product->slug, 0, 6)) . '-' . $product->id;

            // Create inventory
            $inventoryData = [
                'product_id' => $product->id,
                'sku' => $sku,
                'stock_count' => 100, // Default stock
                'sold_count' => 0
            ];
            $inventory = ProductInventory::create($inventoryData);

            // Create product category
            ProductCategory::create([
                'category_id' => $request->input('category'),
                'product_id' => $product->id
            ]);

            // Create product UOM
            ProductUom::create([
                'product_id' => $product->id,
                'unit_id' => $defaultUnit->id,
                'unit_value' => '1'
            ]);

            DB::commit();

            return [
                'product_id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'sku' => $inventory->sku,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'stock_count' => $inventory->stock_count,
                'category_id' => $request->input('category'),
                'description' => $product->description,
                'created_at' => $product->created_at
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
