# WhatsApp Status Posting Setup Guide

This guide explains how to set up and use WhatsApp Status posting in BeePost using either 360Dialog or Vonage WhatsApp Business API.

## Features

- Post text-only WhatsApp Status updates
- Post WhatsApp Status with images
- Post WhatsApp Status with videos  
- Include clickable product URLs in status updates
- Schedule WhatsApp Status posts using existing BeePost scheduler
- Support for both 360Dialog and Vonage WhatsApp Business APIs

## Prerequisites

### For 360Dialog
1. WhatsApp Business Account
2. 360Dialog API account and API key
3. Verified phone number with WhatsApp Business API access

### For Vonage
1. WhatsApp Business Account
2. Vonage API account with WhatsApp Business API access
3. Vonage API key and secret
4. Vonage Application ID and private key

## Configuration

### 1. Update Platform Configuration

The WhatsApp platform is automatically configured in `kode/config/settings.php`. Update the credentials:

```php
'whatsapp' => [
    'name' => "WhatsApp",
    'credential' => [
        'provider' => '360dialog', // or 'vonage'
        '360dialog_api_key' => 'YOUR_360DIALOG_API_KEY',
        '360dialog_base_url' => 'https://waba-v2.360dialog.io',
        'vonage_api_key' => 'YOUR_VONAGE_API_KEY',
        'vonage_api_secret' => 'YOUR_VONAGE_API_SECRET',
        'vonage_application_id' => 'YOUR_VONAGE_APP_ID',
        'vonage_private_key' => 'YOUR_VONAGE_PRIVATE_KEY',
        'phone_number_id' => 'YOUR_WHATSAPP_PHONE_NUMBER_ID',
        'business_account_id' => 'YOUR_BUSINESS_ACCOUNT_ID',
    ],
    'is_integrated' => StatusEnum::true->status(),
    'view_option' => StatusEnum::true->status(),
    'is_feature' => StatusEnum::true->status()
]
```

### 2. Run Database Seeder

Run the platform seeder to add WhatsApp to the database:

```bash
php artisan db:seed --class=PlatformSeeder
```

### 3. Add WhatsApp Account

1. Go to Admin/User Panel → Social Accounts → Add Account
2. Select WhatsApp platform
3. Choose provider (360Dialog or Vonage)
4. Enter required credentials:
   - API Key
   - Phone Number ID
   - Business Account ID

## Usage

### Creating WhatsApp Status Posts

1. Navigate to Social Posts → Create Post
2. Select your WhatsApp account
3. Choose "WHATSAPP_STATUS" as the post type
4. Add your content:
   - **Text**: Enter your status message
   - **Media**: Upload image or video (optional)
   - **Link**: Add product URL or any clickable link (optional)
5. Schedule or post immediately

### Post Types Supported

- **Text Only**: Simple text status updates
- **Text + Image**: Status with image and caption
- **Text + Video**: Status with video and caption
- **Text + Link**: Status with clickable product/website links

### Scheduling

WhatsApp Status posts can be scheduled using the existing BeePost scheduler:

1. Enable scheduling in your subscription package
2. Set schedule date and time when creating the post
3. The post will be automatically sent at the scheduled time

## API Integration Details

### 360Dialog Integration

The system uses 360Dialog's WhatsApp Business API v2:

- **Base URL**: `https://waba-v2.360dialog.io`
- **Authentication**: D360-API-KEY header
- **Endpoint**: `/v1/messages`

### Vonage Integration

Vonage WhatsApp Business API integration:

- **Base URL**: `https://api.nexmo.com`
- **Authentication**: Basic Auth (API Key + Secret)
- **Status**: Implementation placeholder (can be extended)

## File Structure

```
kode/
├── app/
│   ├── Http/Services/Account/whatsapp/
│   │   └── Account.php                    # WhatsApp service implementation
│   ├── Enums/
│   │   └── PostType.php                   # Added WHATSAPP_STATUS type
│   └── Http/Helper/helper.php             # Updated post type badges
├── config/
│   └── settings.php                       # WhatsApp platform configuration
├── resources/views/
│   ├── user/social/post/create.blade.php  # User UI updates
│   └── admin/social/post/create.blade.php # Admin UI updates
├── database/seeders/
│   └── PlatformSeeder.php                 # Auto-includes WhatsApp
└── tests/Feature/
    └── WhatsAppStatusPostingTest.php      # Test cases
```

## Testing

Run the WhatsApp Status posting tests:

```bash
php artisan test tests/Feature/WhatsAppStatusPostingTest.php
```

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Verify your 360Dialog or Vonage API credentials
2. **Phone Number Not Verified**: Ensure your WhatsApp Business phone number is verified
3. **Media Upload Fails**: Check file size limits and supported formats
4. **Status Not Posting**: Verify webhook configuration and API permissions

### Debug Mode

Enable debug logging in your `.env` file:

```env
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed error messages.

## Security Considerations

1. Store API keys securely in environment variables
2. Use HTTPS for all API communications
3. Validate and sanitize all user inputs
4. Implement rate limiting for API calls
5. Monitor API usage and costs

## Limitations

1. WhatsApp Status posts are only visible for 24 hours
2. Media file size limits apply (varies by provider)
3. API rate limits depend on your provider plan
4. Some features may require specific WhatsApp Business API permissions

## Support

For issues related to:
- **360Dialog**: Contact 360Dialog support
- **Vonage**: Contact Vonage support  
- **BeePost Integration**: Check the test cases and implementation in the codebase
