class AppConfig {
  static const String appName = 'Vendora Mobile';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.vendora.com';
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/api/$apiVersion';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  // Pagination
  static const int defaultPageSize = 20;
  
  // Image Upload
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Social Media Platforms
  static const List<String> supportedPlatforms = [
    'facebook',
    'instagram', 
    'twitter',
    'linkedin',
    'whatsapp',
    'telegram'
  ];
}
