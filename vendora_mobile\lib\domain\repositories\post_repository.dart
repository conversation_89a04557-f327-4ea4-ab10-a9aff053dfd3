import 'package:dartz/dartz.dart';
import '../entities/post.dart';
import '../../core/error/failures.dart';

abstract class PostRepository {
  Future<Either<Failure, List<Post>>> getPosts({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, Post>> getPost(String id);

  Future<Either<Failure, Post>> createPost({
    required String content,
    required PostType type,
    required List<String> platforms,
    List<String>? mediaUrls,
    DateTime? scheduledAt,
  });

  Future<Either<Failure, Post>> updatePost({
    required String id,
    String? content,
    List<String>? platforms,
    DateTime? scheduledAt,
  });

  Future<Either<Failure, void>> deletePost(String id);

  Future<Either<Failure, void>> publishPost(String id);

  Future<Either<Failure, PostAnalytics>> getPostAnalytics(String id);

  Future<Either<Failure, List<Post>>> getScheduledPosts();

  Future<Either<Failure, List<Post>>> getDraftPosts();
}
