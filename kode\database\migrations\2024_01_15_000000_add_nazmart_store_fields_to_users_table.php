<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('nazmart_store_id')->nullable()->after('settings');
            $table->string('nazmart_store_slug')->nullable()->after('nazmart_store_id');
            $table->string('nazmart_store_url')->nullable()->after('nazmart_store_slug');
            $table->timestamp('nazmart_store_created_at')->nullable()->after('nazmart_store_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'nazmart_store_id',
                'nazmart_store_slug', 
                'nazmart_store_url',
                'nazmart_store_created_at'
            ]);
        });
    }
};
