import 'package:flutter/foundation.dart';
import '../../domain/entities/store.dart';

enum StoreProviderStatus { initial, loading, loaded, error }

class StoreProvider extends ChangeNotifier {
  StoreProviderStatus _status = StoreProviderStatus.initial;
  Store? _store;
  List<Product> _products = [];
  String? _errorMessage;

  StoreProviderStatus get status => _status;
  Store? get store => _store;
  List<Product> get products => _products;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == StoreProviderStatus.loading;
  bool get hasStore => _store != null;

  Future<void> loadStore() async {
    _setStatus(StoreProviderStatus.loading);

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1));

      // Mock data - user has a store
      _store = Store(
        id: '1',
        userId: '1',
        name: 'My Awesome Store',
        slug: 'my-awesome-store',
        description: 'Welcome to my store! We sell amazing products.',
        status: StoreStatus.active,
        settings: const StoreSettings(
          currency: 'USD',
          isPublic: true,
          allowOrders: true,
          socialLinks: {},
          paymentMethods: {},
        ),
        products: [],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      );

      await loadProducts();
      _setStatus(StoreProviderStatus.loaded);
    } catch (e) {
      _setError('Failed to load store');
    }
  }

  Future<void> loadProducts() async {
    if (_store == null) return;

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(milliseconds: 500));

      // Mock products
      _products = [
        Product(
          id: '1',
          storeId: _store!.id,
          name: 'Premium Headphones',
          description: 'High-quality wireless headphones with noise cancellation',
          price: 199.99,
          image: 'https://example.com/headphones.jpg',
          gallery: [],
          category: 'Electronics',
          stock: 25,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
        Product(
          id: '2',
          storeId: _store!.id,
          name: 'Smartphone Case',
          description: 'Protective case for your smartphone',
          price: 29.99,
          image: 'https://example.com/case.jpg',
          gallery: [],
          category: 'Accessories',
          stock: 50,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ];
    } catch (e) {
      _setError('Failed to load products');
    }
  }

  Future<void> createStore({
    required String name,
    required String description,
  }) async {
    _setStatus(StoreProviderStatus.loading);

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2));

      _store = Store(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: '1',
        name: name,
        slug: name.toLowerCase().replaceAll(' ', '-'),
        description: description,
        status: StoreStatus.active,
        settings: const StoreSettings(
          currency: 'USD',
          isPublic: true,
          allowOrders: true,
          socialLinks: {},
          paymentMethods: {},
        ),
        products: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _setStatus(StoreProviderStatus.loaded);
    } catch (e) {
      _setError('Failed to create store');
    }
  }

  Future<void> addProduct({
    required String name,
    required String description,
    required double price,
    required String category,
    String? image,
  }) async {
    if (_store == null) return;

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1));

      final newProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        storeId: _store!.id,
        name: name,
        description: description,
        price: price,
        image: image,
        gallery: [],
        category: category,
        stock: 0,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _products.insert(0, newProduct);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add product');
    }
  }

  void _setStatus(StoreProviderStatus status) {
    _status = status;
    if (status != StoreProviderStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setStatus(StoreProviderStatus.error);
  }
}
