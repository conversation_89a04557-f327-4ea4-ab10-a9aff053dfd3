.emojiPicker {
    top: 715px !important;
    box-shadow: 0px 3px 15px rgba(0,0,0,0.07) !important;
    background-color: #15192c !important;
}

.emojiPicker nav{
    display: none !important;
}

.emojiPicker .shortcode{
    display: none !important;
}

.emojiPicker .sections{
    height: 202px !important;
    overflow: hidden !important;
    overflow-y: scroll !important;
}



.testi-slider{
    height:  280px !important;
}
  
  

.ratting-progres-5 {
        width: 100%;
}
.ratting-progres-4 {
    width: 70%;
}
.ratting-progres-3 {
    width: 40%;
}
.ratting-progres-2 {
    width: 25%;
}
.ratting-progres-1 {
    width: 15%;
}

.daterangepicker .drp-calendar {
    display: none;
    max-width: unset !important;
}

.image-content-wrap{
    width: 100%;
    height: 260px;
    overflow: hidden;
    margin-bottom: 10px;
}

.image-content-wrap img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlap-list {
    display: flex;
    align-items: center;
  }
  
  .overlap-img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
    margin-left: -10px;
    box-shadow: 0 0 0 1px #ddd;
  }
  
  .image-overlap-list .overlap-img:first-child {
    margin-left: 0;
  }
  
  .more-count {
    width: 30px;
    height: 30px;
    background: #eee;
    color: #333;
    font-size: 12px;
    font-weight: bold;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -10px;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px #ddd;
  }

  .metadata-item{
    padding: 15px 20px;
    border: 1px solid #dadce0;
    border-radius: 8px;

    .metadata-label{
        margin-bottom: 15px;
    }
  }

  .gallery-item {
    position: relative;
    overflow: hidden;
  }
  
  .gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: 0.3s ease;
  }
  
  .gallery-item:hover img {
    transform: scale(1.05);
  }
  
  .gallery-check {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  
  .gallery-check input[type="checkbox"] {
    width: 24px;
    height: 24px;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .image-dropdwon{
    min-width: 100px;
    background-color: var(--color-white);
    padding: 8px 23px 8px 13px !important;
    font-size: 13px !important;
    color: var(--text-secondary);
    height: 38px;
  }

  .image-dropdwon::after {
    content: "";
    font-family: "Bootstrap-icons";
    position: absolute;
    top: 51%;
    right: 9px;
    transform: translateY(-50%) rotate(0deg);
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    border-top: unset;
    border-right: unset;
    border-bottom: 0;
    border-left: unset;
    font-size: 12px;
    font-weight: 800;
    transition: .4s ease;
}

.image-card  img{
  width: 100%;
  height: 300px;
  object-fit: cover;
}