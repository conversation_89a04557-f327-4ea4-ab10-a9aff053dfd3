<?php

namespace Tests\Feature;

use App\Enums\PostType;
use App\Enums\StatusEnum;
use App\Http\Services\Account\whatsapp\Account as WhatsAppAccount;
use App\Models\MediaPlatform;
use App\Models\SocialAccount;
use App\Models\SocialPost;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class WhatsAppStatusPostingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $whatsappPlatform;
    protected $whatsappAccount;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create WhatsApp platform
        $this->whatsappPlatform = MediaPlatform::create([
            'name' => 'WhatsApp',
            'slug' => 'whatsapp',
            'url' => 'https://wa.me',
            'description' => 'WhatsApp Business API',
            'configuration' => (object) [
                'provider' => '360dialog',
                '360dialog_api_key' => 'test_api_key',
                '360dialog_base_url' => 'https://waba-v2.360dialog.io',
                'phone_number_id' => '**********',
                'business_account_id' => 'test_business_id'
            ],
            'status' => StatusEnum::true->status(),
            'is_integrated' => StatusEnum::true->status(),
            'is_feature' => StatusEnum::true->status()
        ]);
        
        // Create WhatsApp account
        $this->whatsappAccount = SocialAccount::create([
            'user_id' => $this->user->id,
            'platform_id' => $this->whatsappPlatform->id,
            'account_id' => '**********',
            'name' => 'WhatsApp Business - **********',
            'username' => '**********',
            'token' => 'test_api_key',
            'provider' => '360dialog',
            'status' => StatusEnum::true->status()
        ]);
    }

    /** @test */
    public function it_can_create_whatsapp_platform_from_config()
    {
        $platforms = config('settings.platforms');
        
        $this->assertArrayHasKey('whatsapp', $platforms);
        $this->assertEquals('WhatsApp', $platforms['whatsapp']['name']);
        $this->assertArrayHasKey('360dialog_api_key', $platforms['whatsapp']['credential']);
        $this->assertArrayHasKey('vonage_api_key', $platforms['whatsapp']['credential']);
    }

    /** @test */
    public function it_can_validate_360dialog_connection()
    {
        Http::fake([
            'waba-v2.360dialog.io/v1/configs/webhook' => Http::response(['status' => 'success'], 200)
        ]);

        $whatsappService = new WhatsAppAccount();
        
        $request = [
            'provider' => '360dialog',
            'api_key' => 'test_api_key',
            'phone_number_id' => '**********',
            'business_account_id' => 'test_business_id'
        ];

        $result = $whatsappService->whatsapp($this->whatsappPlatform, $request, 'admin');
        
        $this->assertEquals('success', $result['status']);
    }

    /** @test */
    public function it_can_send_text_only_whatsapp_status()
    {
        Http::fake([
            'waba-v2.360dialog.io/v1/messages' => Http::response([
                'messages' => [
                    ['id' => 'test_message_id']
                ]
            ], 200)
        ]);

        $post = SocialPost::create([
            'account_id' => $this->whatsappAccount->id,
            'platform_id' => $this->whatsappPlatform->id,
            'user_id' => $this->user->id,
            'content' => 'Test WhatsApp Status message',
            'post_type' => PostType::WHATSAPP_STATUS->value,
            'status' => '0' // Pending
        ]);

        $whatsappService = new WhatsAppAccount();
        $result = $whatsappService->send($post);

        $this->assertTrue($result['status']);
        $this->assertStringContains('successfully', $result['response']);
        $this->assertStringContains('wa.me', $result['url']);
    }

    /** @test */
    public function it_can_send_whatsapp_status_with_product_link()
    {
        Http::fake([
            'waba-v2.360dialog.io/v1/messages' => Http::response([
                'messages' => [
                    ['id' => 'test_message_id']
                ]
            ], 200)
        ]);

        $post = SocialPost::create([
            'account_id' => $this->whatsappAccount->id,
            'platform_id' => $this->whatsappPlatform->id,
            'user_id' => $this->user->id,
            'content' => 'Check out our new product!',
            'link' => 'https://example.com/product/123',
            'post_type' => PostType::WHATSAPP_STATUS->value,
            'status' => '0' // Pending
        ]);

        $whatsappService = new WhatsAppAccount();
        $result = $whatsappService->send($post);

        $this->assertTrue($result['status']);
        $this->assertStringContains('successfully', $result['response']);
    }

    /** @test */
    public function it_handles_whatsapp_api_errors_gracefully()
    {
        Http::fake([
            'waba-v2.360dialog.io/v1/messages' => Http::response([
                'error' => [
                    'message' => 'Invalid phone number'
                ]
            ], 400)
        ]);

        $post = SocialPost::create([
            'account_id' => $this->whatsappAccount->id,
            'platform_id' => $this->whatsappPlatform->id,
            'user_id' => $this->user->id,
            'content' => 'Test message',
            'post_type' => PostType::WHATSAPP_STATUS->value,
            'status' => '0' // Pending
        ]);

        $whatsappService = new WhatsAppAccount();
        $result = $whatsappService->send($post);

        $this->assertFalse($result['status']);
        $this->assertStringContains('Invalid phone number', $result['response']);
    }

    /** @test */
    public function it_only_allows_whatsapp_status_post_type_for_whatsapp_platform()
    {
        $postTypes = \App\Enums\PostType::toArray();
        
        // Simulate the UI logic for WhatsApp platform
        $whatsappPostTypes = array_intersect_key($postTypes, array_flip([\App\Enums\PostType::WHATSAPP_STATUS->name]));
        
        $this->assertCount(1, $whatsappPostTypes);
        $this->assertArrayHasKey('WHATSAPP_STATUS', $whatsappPostTypes);
        $this->assertEquals(PostType::WHATSAPP_STATUS->value, $whatsappPostTypes['WHATSAPP_STATUS']);
    }

    /** @test */
    public function it_includes_whatsapp_in_platform_connection_fields()
    {
        $connectionFields = config('settings.platforms_connetion_field');
        
        $this->assertArrayHasKey('whatsapp', $connectionFields);
        $this->assertContains('provider', $connectionFields['whatsapp']);
        $this->assertContains('api_key', $connectionFields['whatsapp']);
        $this->assertContains('phone_number_id', $connectionFields['whatsapp']);
        $this->assertContains('business_account_id', $connectionFields['whatsapp']);
    }
}
