import 'package:dartz/dartz.dart';
import '../entities/store.dart';
import '../../core/error/failures.dart';

abstract class StoreRepository {
  Future<Either<Failure, Store>> getStore(String id);

  Future<Either<Failure, Store?>> getUserStore();

  Future<Either<Failure, Store>> createStore({
    required String name,
    required String description,
    String? logo,
    String? banner,
  });

  Future<Either<Failure, Store>> updateStore({
    required String id,
    String? name,
    String? description,
    String? logo,
    String? banner,
    StoreSettings? settings,
  });

  Future<Either<Failure, void>> deleteStore(String id);

  Future<Either<Failure, List<Product>>> getProducts({
    required String storeId,
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, Product>> createProduct({
    required String storeId,
    required String name,
    required String description,
    required double price,
    required String category,
    String? image,
    List<String>? gallery,
    int stock = 0,
  });

  Future<Either<Failure, Product>> updateProduct({
    required String id,
    String? name,
    String? description,
    double? price,
    String? category,
    String? image,
    List<String>? gallery,
    int? stock,
    bool? isActive,
  });

  Future<Either<Failure, void>> deleteProduct(String id);
}
